[pytest]
minversion = 3.10
norecursedirs = test_* extra_*
xfail_strict = True
addopts =
    # show summary of tests
    -ra
    # capture only Python print and C++ py::print, but not C output (low-level Python errors)
    --capture=sys
    # Show local info when a failure occurs
    --showlocals
log_cli_level = info
filterwarnings =
    # make warnings into errors but ignore certain third-party extension issues
    error
    # somehow, some DeprecationWarnings do not get turned into errors
    always::DeprecationWarning
    # importing scipy submodules on some version of Python
    ignore::ImportWarning
    # bogus numpy ABI warning (see numpy/#432)
    ignore:.*numpy.dtype size changed.*:RuntimeWarning
    ignore:.*numpy.ufunc size changed.*:RuntimeWarning
