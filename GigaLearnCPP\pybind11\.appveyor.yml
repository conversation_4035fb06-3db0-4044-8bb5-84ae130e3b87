version: 1.0.{build}
image:
- Visual Studio 2017
test: off
skip_branch_with_pr: true
build:
  parallel: true
platform:
- x86
environment:
  matrix:
  - PYTHON: 36
    CONFIG: Debug
install:
- ps: |
    $env:CMAKE_GENERATOR = "Visual Studio 15 2017"
    if ($env:PLATFORM -eq "x64") { $env:PYTHON = "$env:PYTHON-x64" }
    $env:PATH = "C:\Python$env:PYTHON\;C:\Python$env:PYTHON\Scripts\;$env:PATH"
    python -W ignore -m pip install --upgrade pip wheel
    python -W ignore -m pip install pytest numpy --no-warn-script-location pytest-timeout
- ps: |
    Start-FileDownload 'https://gitlab.com/libeigen/eigen/-/archive/3.3.7/eigen-3.3.7.zip'
    7z x eigen-3.3.7.zip -y > $null
    $env:CMAKE_INCLUDE_PATH = "eigen-3.3.7;$env:CMAKE_INCLUDE_PATH"
build_script:
- cmake -G "%CMAKE_GENERATOR%" -A "%CMAKE_ARCH%"
    -DCMAKE_CXX_STANDARD=14
    -DPYBIND11_WERROR=ON
    -DDOWNLOAD_CATCH=ON
    -DCMAKE_SUPPRESS_REGENERATION=1
    .
- set MSBuildLogger="C:\Program Files\AppVeyor\BuildAgent\Appveyor.MSBuildLogger.dll"
- cmake --build . --config %CONFIG% --target pytest -- /m /v:m /logger:%MSBuildLogger%
- cmake --build . --config %CONFIG% --target cpptest -- /m /v:m /logger:%MSBuildLogger%
on_failure: if exist "tests\test_cmake_build" type tests\test_cmake_build\*.log*
