[RLBot Configuration]
# Visit https://github.com/RLBot/RLBot/wiki/Config-File-Documentation to see what you can put here.

[Team Configuration]
# Visit https://github.com/RLBot/RLBot/wiki/Config-File-Documentation to see what you can put here.

[Match Configuration]
# Number of bots/players which will be spawned.  We support up to max 10.
num_participants = 2
game_mode = Soccer
game_map = Mannfield

[Mutator Configuration]
# Visit https://github.com/RLBot/RLBot/wiki/Config-File-Documentation to see what you can put here.
Match Length = Unlimited

[Participant Configuration]
# Put the name of your bot config file here.  Only num_participants config files will be read!
# Everything needs a config, even players and default bots.  We still set loadouts and names from config!
participant_config_0 = CppPythonAgent.cfg
participant_config_1 = CppPythonAgent.cfg
participant_config_2 = CppPythonAgent.cfg
participant_config_3 = CppPythonAgent.cfg
participant_config_4 = CppPythonAgent.cfg
participant_config_5 = CppPythonAgent.cfg
participant_config_6 = CppPythonAgent.cfg
participant_config_7 = CppPythonAgent.cfg
participant_config_8 = CppPythonAgent.cfg
participant_config_9 = CppPythonAgent.cfg

# team 0 shoots on positive goal, team 1 shoots on negative goal
participant_team_0 = 0
participant_team_1 = 1
participant_team_2 = 0
participant_team_3 = 1
participant_team_4 = 0
participant_team_5 = 1
participant_team_6 = 0
participant_team_7 = 1
participant_team_8 = 0
participant_team_9 = 1

# Accepted values are "human", "rlbot", "psyonix", and "party_member_bot"
# You can have up to 4 local players and they must be activated in game or it will crash.
# If no player is specified you will be spawned in as spectator!
# human - not controlled by the framework
# rlbot - controlled by the framework
# psyonix - default bots (skill level can be changed with participant_bot_skill
# party_member_bot - controlled by the framework but the game detects it as a human
participant_type_0 = rlbot
participant_type_1 = rlbot
participant_type_2 = rlbot
participant_type_3 = rlbot
participant_type_4 = rlbot
participant_type_5 = rlbot
participant_type_6 = rlbot
participant_type_7 = rlbot
participant_type_8 = rlbot
participant_type_9 = rlbot


# If participant is a bot and not RLBot controlled, this value will be used to set bot skill.
# 0.0 is Rookie, 0.5 is pro, 1.0 is all-star.  You can set values in-between as well.
# Please leave a value here even if it isn't used :)
participant_bot_skill_0 = 1.0
participant_bot_skill_1 = 1.0
participant_bot_skill_2 = 1.0
participant_bot_skill_3 = 1.0
participant_bot_skill_4 = 1.0
participant_bot_skill_5 = 1.0
participant_bot_skill_6 = 1.0
participant_bot_skill_7 = 1.0
participant_bot_skill_8 = 1.0
participant_bot_skill_9 = 1.0
