// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_RLBOT_RLBOT_FLAT_H_
#define FLATBUFFERS_GENERATED_RLBOT_RLBOT_FLAT_H_

#include "flatbuffers/flatbuffers.h"

namespace rlbot {
namespace flat {

struct ControllerState;

struct PlayerInput;

struct Vector3;

struct Rotator;

struct Quaternion;

struct BoxShape;

struct SphereShape;

struct CylinderShape;

struct Touch;

struct ScoreInfo;

struct Physics;

struct PlayerInfo;

struct DropShotBallInfo;

struct BallInfo;

struct BoostPadState;

struct DropshotTile;

struct GameInfo;

struct TeamInfo;

struct GameTickPacket;

struct RigidBodyState;

struct PlayerRigidBodyState;

struct BallRigidBodyState;

struct RigidBodyTick;

struct GoalInfo;

struct BoostPad;

struct FieldInfo;

struct Float;

struct Bool;

struct Vector3Partial;

struct RotatorPartial;

struct DesiredPhysics;

struct DesiredBallState;

struct DesiredCarState;

struct DesiredBoostState;

struct DesiredGameInfoState;

struct ConsoleCommand;

struct DesiredGameState;

struct Color;

struct RenderMessage;

struct RenderGroup;

struct QuickChat;

struct TinyPlayer;

struct TinyBall;

struct TinyPacket;

struct PredictionSlice;

struct BallPrediction;

struct RLBotPlayer;

struct HumanPlayer;

struct PsyonixBotPlayer;

struct PartyMemberBotPlayer;

struct PlayerLoadout;

struct LoadoutPaint;

struct PlayerConfiguration;

struct MutatorSettings;

struct MatchSettings;

struct QuickChatMessages;

struct ReadyMessage;

struct PlayerStatEvent;

struct PlayerSpectate;

struct PlayerInputChange;

struct GameMessageWrapper;

struct MessagePacket;

enum CollisionShape {
  CollisionShape_NONE = 0,
  CollisionShape_BoxShape = 1,
  CollisionShape_SphereShape = 2,
  CollisionShape_CylinderShape = 3,
  CollisionShape_MIN = CollisionShape_NONE,
  CollisionShape_MAX = CollisionShape_CylinderShape
};

inline const CollisionShape (&EnumValuesCollisionShape())[4] {
  static const CollisionShape values[] = {
    CollisionShape_NONE,
    CollisionShape_BoxShape,
    CollisionShape_SphereShape,
    CollisionShape_CylinderShape
  };
  return values;
}

inline const char * const *EnumNamesCollisionShape() {
  static const char * const names[] = {
    "NONE",
    "BoxShape",
    "SphereShape",
    "CylinderShape",
    nullptr
  };
  return names;
}

inline const char *EnumNameCollisionShape(CollisionShape e) {
  const size_t index = static_cast<int>(e);
  return EnumNamesCollisionShape()[index];
}

template<typename T> struct CollisionShapeTraits {
  static const CollisionShape enum_value = CollisionShape_NONE;
};

template<> struct CollisionShapeTraits<BoxShape> {
  static const CollisionShape enum_value = CollisionShape_BoxShape;
};

template<> struct CollisionShapeTraits<SphereShape> {
  static const CollisionShape enum_value = CollisionShape_SphereShape;
};

template<> struct CollisionShapeTraits<CylinderShape> {
  static const CollisionShape enum_value = CollisionShape_CylinderShape;
};

bool VerifyCollisionShape(flatbuffers::Verifier &verifier, const void *obj, CollisionShape type);
bool VerifyCollisionShapeVector(flatbuffers::Verifier &verifier, const flatbuffers::Vector<flatbuffers::Offset<void>> *values, const flatbuffers::Vector<uint8_t> *types);

enum TileState {
  TileState_Unknown = 0  /// The default state of the tiles.
,
  TileState_Filled = 1  /// The state when a tile has been damaged.
,
  TileState_Damaged = 2  /// The state of a tile when it is open and a goal can be scored.
,
  TileState_Open = 3,
  TileState_MIN = TileState_Unknown,
  TileState_MAX = TileState_Open
};

inline const TileState (&EnumValuesTileState())[4] {
  static const TileState values[] = {
    TileState_Unknown,
    TileState_Filled,
    TileState_Damaged,
    TileState_Open
  };
  return values;
}

inline const char * const *EnumNamesTileState() {
  static const char * const names[] = {
    "Unknown",
    "Filled",
    "Damaged",
    "Open",
    nullptr
  };
  return names;
}

inline const char *EnumNameTileState(TileState e) {
  const size_t index = static_cast<int>(e);
  return EnumNamesTileState()[index];
}

enum RenderType {
  RenderType_DrawLine2D = 1,
  RenderType_DrawLine3D = 2,
  RenderType_DrawLine2D_3D = 3,
  RenderType_DrawRect2D = 4,
  RenderType_DrawRect3D = 5,
  RenderType_DrawString2D = 6,
  RenderType_DrawString3D = 7,
  RenderType_DrawCenteredRect3D = 8,
  RenderType_MIN = RenderType_DrawLine2D,
  RenderType_MAX = RenderType_DrawCenteredRect3D
};

inline const RenderType (&EnumValuesRenderType())[8] {
  static const RenderType values[] = {
    RenderType_DrawLine2D,
    RenderType_DrawLine3D,
    RenderType_DrawLine2D_3D,
    RenderType_DrawRect2D,
    RenderType_DrawRect3D,
    RenderType_DrawString2D,
    RenderType_DrawString3D,
    RenderType_DrawCenteredRect3D
  };
  return values;
}

inline const char * const *EnumNamesRenderType() {
  static const char * const names[] = {
    "DrawLine2D",
    "DrawLine3D",
    "DrawLine2D_3D",
    "DrawRect2D",
    "DrawRect3D",
    "DrawString2D",
    "DrawString3D",
    "DrawCenteredRect3D",
    nullptr
  };
  return names;
}

inline const char *EnumNameRenderType(RenderType e) {
  const size_t index = static_cast<int>(e) - static_cast<int>(RenderType_DrawLine2D);
  return EnumNamesRenderType()[index];
}

enum QuickChatSelection {
  QuickChatSelection_Information_IGotIt = 0,
  QuickChatSelection_Information_NeedBoost = 1,
  QuickChatSelection_Information_TakeTheShot = 2,
  QuickChatSelection_Information_Defending = 3,
  QuickChatSelection_Information_GoForIt = 4,
  QuickChatSelection_Information_Centering = 5,
  QuickChatSelection_Information_AllYours = 6,
  QuickChatSelection_Information_InPosition = 7,
  QuickChatSelection_Information_Incoming = 8,
  QuickChatSelection_Compliments_NiceShot = 9,
  QuickChatSelection_Compliments_GreatPass = 10,
  QuickChatSelection_Compliments_Thanks = 11,
  QuickChatSelection_Compliments_WhatASave = 12,
  QuickChatSelection_Compliments_NiceOne = 13,
  QuickChatSelection_Compliments_WhatAPlay = 14,
  QuickChatSelection_Compliments_GreatClear = 15,
  QuickChatSelection_Compliments_NiceBlock = 16,
  QuickChatSelection_Reactions_OMG = 17,
  QuickChatSelection_Reactions_Noooo = 18,
  QuickChatSelection_Reactions_Wow = 19,
  QuickChatSelection_Reactions_CloseOne = 20,
  QuickChatSelection_Reactions_NoWay = 21,
  QuickChatSelection_Reactions_HolyCow = 22,
  QuickChatSelection_Reactions_Whew = 23,
  QuickChatSelection_Reactions_Siiiick = 24,
  QuickChatSelection_Reactions_Calculated = 25,
  QuickChatSelection_Reactions_Savage = 26,
  QuickChatSelection_Reactions_Okay = 27,
  QuickChatSelection_Apologies_Cursing = 28,
  QuickChatSelection_Apologies_NoProblem = 29,
  QuickChatSelection_Apologies_Whoops = 30,
  QuickChatSelection_Apologies_Sorry = 31,
  QuickChatSelection_Apologies_MyBad = 32,
  QuickChatSelection_Apologies_Oops = 33,
  QuickChatSelection_Apologies_MyFault = 34,
  QuickChatSelection_PostGame_Gg = 35,
  QuickChatSelection_PostGame_WellPlayed = 36,
  QuickChatSelection_PostGame_ThatWasFun = 37,
  QuickChatSelection_PostGame_Rematch = 38,
  QuickChatSelection_PostGame_OneMoreGame = 39,
  QuickChatSelection_PostGame_WhatAGame = 40,
  QuickChatSelection_PostGame_NiceMoves = 41,
  QuickChatSelection_PostGame_EverybodyDance = 42  /// Custom text chats made by bot makers
,
  QuickChatSelection_MaxPysonixQuickChatPresets = 43  /// Waste of CPU cycles
,
  QuickChatSelection_Custom_Toxic_WasteCPU = 44  /// Git gud*
,
  QuickChatSelection_Custom_Toxic_GitGut = 45  /// De-Allocate Yourself
,
  QuickChatSelection_Custom_Toxic_DeAlloc = 46  /// 404: Your skill not found
,
  QuickChatSelection_Custom_Toxic_404NoSkill = 47  /// Get a virus
,
  QuickChatSelection_Custom_Toxic_CatchVirus = 48  /// Passing!
,
  QuickChatSelection_Custom_Useful_Passing = 49  /// Faking!
,
  QuickChatSelection_Custom_Useful_Faking = 50  /// Demoing!
,
  QuickChatSelection_Custom_Useful_Demoing = 51  /// BOOPING
,
  QuickChatSelection_Custom_Useful_Bumping = 52  /// The chances of that was 47525 to 1*
,
  QuickChatSelection_Custom_Compliments_TinyChances = 53  /// Who upped your skill level?
,
  QuickChatSelection_Custom_Compliments_SkillLevel = 54  /// Your programmer should be proud
,
  QuickChatSelection_Custom_Compliments_proud = 55  /// You're the GC of Bots
,
  QuickChatSelection_Custom_Compliments_GC = 56  /// Are you <Insert Pro>Bot? *
,
  QuickChatSelection_Custom_Compliments_Pro = 57  /// Lag
,
  QuickChatSelection_Custom_Excuses_Lag = 58  /// Ghost inputs
,
  QuickChatSelection_Custom_Excuses_GhostInputs = 59  /// RIGGED
,
  QuickChatSelection_Custom_Excuses_Rigged = 60  /// Mafia plays!
,
  QuickChatSelection_Custom_Toxic_MafiaPlays = 61  /// Yeet!
,
  QuickChatSelection_Custom_Exclamation_Yeet = 62,
  QuickChatSelection_MIN = QuickChatSelection_Information_IGotIt,
  QuickChatSelection_MAX = QuickChatSelection_Custom_Exclamation_Yeet
};

inline const QuickChatSelection (&EnumValuesQuickChatSelection())[63] {
  static const QuickChatSelection values[] = {
    QuickChatSelection_Information_IGotIt,
    QuickChatSelection_Information_NeedBoost,
    QuickChatSelection_Information_TakeTheShot,
    QuickChatSelection_Information_Defending,
    QuickChatSelection_Information_GoForIt,
    QuickChatSelection_Information_Centering,
    QuickChatSelection_Information_AllYours,
    QuickChatSelection_Information_InPosition,
    QuickChatSelection_Information_Incoming,
    QuickChatSelection_Compliments_NiceShot,
    QuickChatSelection_Compliments_GreatPass,
    QuickChatSelection_Compliments_Thanks,
    QuickChatSelection_Compliments_WhatASave,
    QuickChatSelection_Compliments_NiceOne,
    QuickChatSelection_Compliments_WhatAPlay,
    QuickChatSelection_Compliments_GreatClear,
    QuickChatSelection_Compliments_NiceBlock,
    QuickChatSelection_Reactions_OMG,
    QuickChatSelection_Reactions_Noooo,
    QuickChatSelection_Reactions_Wow,
    QuickChatSelection_Reactions_CloseOne,
    QuickChatSelection_Reactions_NoWay,
    QuickChatSelection_Reactions_HolyCow,
    QuickChatSelection_Reactions_Whew,
    QuickChatSelection_Reactions_Siiiick,
    QuickChatSelection_Reactions_Calculated,
    QuickChatSelection_Reactions_Savage,
    QuickChatSelection_Reactions_Okay,
    QuickChatSelection_Apologies_Cursing,
    QuickChatSelection_Apologies_NoProblem,
    QuickChatSelection_Apologies_Whoops,
    QuickChatSelection_Apologies_Sorry,
    QuickChatSelection_Apologies_MyBad,
    QuickChatSelection_Apologies_Oops,
    QuickChatSelection_Apologies_MyFault,
    QuickChatSelection_PostGame_Gg,
    QuickChatSelection_PostGame_WellPlayed,
    QuickChatSelection_PostGame_ThatWasFun,
    QuickChatSelection_PostGame_Rematch,
    QuickChatSelection_PostGame_OneMoreGame,
    QuickChatSelection_PostGame_WhatAGame,
    QuickChatSelection_PostGame_NiceMoves,
    QuickChatSelection_PostGame_EverybodyDance,
    QuickChatSelection_MaxPysonixQuickChatPresets,
    QuickChatSelection_Custom_Toxic_WasteCPU,
    QuickChatSelection_Custom_Toxic_GitGut,
    QuickChatSelection_Custom_Toxic_DeAlloc,
    QuickChatSelection_Custom_Toxic_404NoSkill,
    QuickChatSelection_Custom_Toxic_CatchVirus,
    QuickChatSelection_Custom_Useful_Passing,
    QuickChatSelection_Custom_Useful_Faking,
    QuickChatSelection_Custom_Useful_Demoing,
    QuickChatSelection_Custom_Useful_Bumping,
    QuickChatSelection_Custom_Compliments_TinyChances,
    QuickChatSelection_Custom_Compliments_SkillLevel,
    QuickChatSelection_Custom_Compliments_proud,
    QuickChatSelection_Custom_Compliments_GC,
    QuickChatSelection_Custom_Compliments_Pro,
    QuickChatSelection_Custom_Excuses_Lag,
    QuickChatSelection_Custom_Excuses_GhostInputs,
    QuickChatSelection_Custom_Excuses_Rigged,
    QuickChatSelection_Custom_Toxic_MafiaPlays,
    QuickChatSelection_Custom_Exclamation_Yeet
  };
  return values;
}

inline const char * const *EnumNamesQuickChatSelection() {
  static const char * const names[] = {
    "Information_IGotIt",
    "Information_NeedBoost",
    "Information_TakeTheShot",
    "Information_Defending",
    "Information_GoForIt",
    "Information_Centering",
    "Information_AllYours",
    "Information_InPosition",
    "Information_Incoming",
    "Compliments_NiceShot",
    "Compliments_GreatPass",
    "Compliments_Thanks",
    "Compliments_WhatASave",
    "Compliments_NiceOne",
    "Compliments_WhatAPlay",
    "Compliments_GreatClear",
    "Compliments_NiceBlock",
    "Reactions_OMG",
    "Reactions_Noooo",
    "Reactions_Wow",
    "Reactions_CloseOne",
    "Reactions_NoWay",
    "Reactions_HolyCow",
    "Reactions_Whew",
    "Reactions_Siiiick",
    "Reactions_Calculated",
    "Reactions_Savage",
    "Reactions_Okay",
    "Apologies_Cursing",
    "Apologies_NoProblem",
    "Apologies_Whoops",
    "Apologies_Sorry",
    "Apologies_MyBad",
    "Apologies_Oops",
    "Apologies_MyFault",
    "PostGame_Gg",
    "PostGame_WellPlayed",
    "PostGame_ThatWasFun",
    "PostGame_Rematch",
    "PostGame_OneMoreGame",
    "PostGame_WhatAGame",
    "PostGame_NiceMoves",
    "PostGame_EverybodyDance",
    "MaxPysonixQuickChatPresets",
    "Custom_Toxic_WasteCPU",
    "Custom_Toxic_GitGut",
    "Custom_Toxic_DeAlloc",
    "Custom_Toxic_404NoSkill",
    "Custom_Toxic_CatchVirus",
    "Custom_Useful_Passing",
    "Custom_Useful_Faking",
    "Custom_Useful_Demoing",
    "Custom_Useful_Bumping",
    "Custom_Compliments_TinyChances",
    "Custom_Compliments_SkillLevel",
    "Custom_Compliments_proud",
    "Custom_Compliments_GC",
    "Custom_Compliments_Pro",
    "Custom_Excuses_Lag",
    "Custom_Excuses_GhostInputs",
    "Custom_Excuses_Rigged",
    "Custom_Toxic_MafiaPlays",
    "Custom_Exclamation_Yeet",
    nullptr
  };
  return names;
}

inline const char *EnumNameQuickChatSelection(QuickChatSelection e) {
  const size_t index = static_cast<int>(e);
  return EnumNamesQuickChatSelection()[index];
}

enum PlayerClass {
  PlayerClass_NONE = 0,
  PlayerClass_RLBotPlayer = 1,
  PlayerClass_HumanPlayer = 2,
  PlayerClass_PsyonixBotPlayer = 3,
  PlayerClass_PartyMemberBotPlayer = 4,
  PlayerClass_MIN = PlayerClass_NONE,
  PlayerClass_MAX = PlayerClass_PartyMemberBotPlayer
};

inline const PlayerClass (&EnumValuesPlayerClass())[5] {
  static const PlayerClass values[] = {
    PlayerClass_NONE,
    PlayerClass_RLBotPlayer,
    PlayerClass_HumanPlayer,
    PlayerClass_PsyonixBotPlayer,
    PlayerClass_PartyMemberBotPlayer
  };
  return values;
}

inline const char * const *EnumNamesPlayerClass() {
  static const char * const names[] = {
    "NONE",
    "RLBotPlayer",
    "HumanPlayer",
    "PsyonixBotPlayer",
    "PartyMemberBotPlayer",
    nullptr
  };
  return names;
}

inline const char *EnumNamePlayerClass(PlayerClass e) {
  const size_t index = static_cast<int>(e);
  return EnumNamesPlayerClass()[index];
}

template<typename T> struct PlayerClassTraits {
  static const PlayerClass enum_value = PlayerClass_NONE;
};

template<> struct PlayerClassTraits<RLBotPlayer> {
  static const PlayerClass enum_value = PlayerClass_RLBotPlayer;
};

template<> struct PlayerClassTraits<HumanPlayer> {
  static const PlayerClass enum_value = PlayerClass_HumanPlayer;
};

template<> struct PlayerClassTraits<PsyonixBotPlayer> {
  static const PlayerClass enum_value = PlayerClass_PsyonixBotPlayer;
};

template<> struct PlayerClassTraits<PartyMemberBotPlayer> {
  static const PlayerClass enum_value = PlayerClass_PartyMemberBotPlayer;
};

bool VerifyPlayerClass(flatbuffers::Verifier &verifier, const void *obj, PlayerClass type);
bool VerifyPlayerClassVector(flatbuffers::Verifier &verifier, const flatbuffers::Vector<flatbuffers::Offset<void>> *values, const flatbuffers::Vector<uint8_t> *types);

enum GameMode {
  GameMode_Soccer = 0,
  GameMode_Hoops = 1,
  GameMode_Dropshot = 2,
  GameMode_Hockey = 3,
  GameMode_Rumble = 4,
  GameMode_Heatseeker = 5,
  GameMode_MIN = GameMode_Soccer,
  GameMode_MAX = GameMode_Heatseeker
};

inline const GameMode (&EnumValuesGameMode())[6] {
  static const GameMode values[] = {
    GameMode_Soccer,
    GameMode_Hoops,
    GameMode_Dropshot,
    GameMode_Hockey,
    GameMode_Rumble,
    GameMode_Heatseeker
  };
  return values;
}

inline const char * const *EnumNamesGameMode() {
  static const char * const names[] = {
    "Soccer",
    "Hoops",
    "Dropshot",
    "Hockey",
    "Rumble",
    "Heatseeker",
    nullptr
  };
  return names;
}

inline const char *EnumNameGameMode(GameMode e) {
  const size_t index = static_cast<int>(e);
  return EnumNamesGameMode()[index];
}

enum GameMap {
  GameMap_DFHStadium = 0,
  GameMap_Mannfield = 1,
  GameMap_ChampionsField = 2,
  GameMap_UrbanCentral = 3,
  GameMap_BeckwithPark = 4,
  GameMap_UtopiaColiseum = 5,
  GameMap_Wasteland = 6,
  GameMap_NeoTokyo = 7,
  GameMap_AquaDome = 8,
  GameMap_StarbaseArc = 9,
  GameMap_Farmstead = 10,
  GameMap_SaltyShores = 11,
  GameMap_DFHStadium_Stormy = 12,
  GameMap_DFHStadium_Day = 13,
  GameMap_Mannfield_Stormy = 14,
  GameMap_Mannfield_Night = 15,
  GameMap_ChampionsField_Day = 16,
  GameMap_BeckwithPark_Stormy = 17,
  GameMap_BeckwithPark_Midnight = 18,
  GameMap_UrbanCentral_Night = 19,
  GameMap_UrbanCentral_Dawn = 20,
  GameMap_UtopiaColiseum_Dusk = 21,
  GameMap_DFHStadium_Snowy = 22,
  GameMap_Mannfield_Snowy = 23,
  GameMap_UtopiaColiseum_Snowy = 24,
  GameMap_Badlands = 25,
  GameMap_Badlands_Night = 26,
  GameMap_TokyoUnderpass = 27,
  GameMap_Arctagon = 28,
  GameMap_Pillars = 29,
  GameMap_Cosmic = 30,
  GameMap_DoubleGoal = 31,
  GameMap_Octagon = 32,
  GameMap_Underpass = 33,
  GameMap_UtopiaRetro = 34,
  GameMap_Hoops_DunkHouse = 35,
  GameMap_DropShot_Core707 = 36,
  GameMap_ThrowbackStadium = 37,
  GameMap_ForbiddenTemple = 38,
  GameMap_RivalsArena = 39,
  GameMap_Farmstead_Night = 40,
  GameMap_SaltyShores_Night = 41,
  GameMap_NeonFields = 42,
  GameMap_MIN = GameMap_DFHStadium,
  GameMap_MAX = GameMap_NeonFields
};

inline const GameMap (&EnumValuesGameMap())[43] {
  static const GameMap values[] = {
    GameMap_DFHStadium,
    GameMap_Mannfield,
    GameMap_ChampionsField,
    GameMap_UrbanCentral,
    GameMap_BeckwithPark,
    GameMap_UtopiaColiseum,
    GameMap_Wasteland,
    GameMap_NeoTokyo,
    GameMap_AquaDome,
    GameMap_StarbaseArc,
    GameMap_Farmstead,
    GameMap_SaltyShores,
    GameMap_DFHStadium_Stormy,
    GameMap_DFHStadium_Day,
    GameMap_Mannfield_Stormy,
    GameMap_Mannfield_Night,
    GameMap_ChampionsField_Day,
    GameMap_BeckwithPark_Stormy,
    GameMap_BeckwithPark_Midnight,
    GameMap_UrbanCentral_Night,
    GameMap_UrbanCentral_Dawn,
    GameMap_UtopiaColiseum_Dusk,
    GameMap_DFHStadium_Snowy,
    GameMap_Mannfield_Snowy,
    GameMap_UtopiaColiseum_Snowy,
    GameMap_Badlands,
    GameMap_Badlands_Night,
    GameMap_TokyoUnderpass,
    GameMap_Arctagon,
    GameMap_Pillars,
    GameMap_Cosmic,
    GameMap_DoubleGoal,
    GameMap_Octagon,
    GameMap_Underpass,
    GameMap_UtopiaRetro,
    GameMap_Hoops_DunkHouse,
    GameMap_DropShot_Core707,
    GameMap_ThrowbackStadium,
    GameMap_ForbiddenTemple,
    GameMap_RivalsArena,
    GameMap_Farmstead_Night,
    GameMap_SaltyShores_Night,
    GameMap_NeonFields
  };
  return values;
}

inline const char * const *EnumNamesGameMap() {
  static const char * const names[] = {
    "DFHStadium",
    "Mannfield",
    "ChampionsField",
    "UrbanCentral",
    "BeckwithPark",
    "UtopiaColiseum",
    "Wasteland",
    "NeoTokyo",
    "AquaDome",
    "StarbaseArc",
    "Farmstead",
    "SaltyShores",
    "DFHStadium_Stormy",
    "DFHStadium_Day",
    "Mannfield_Stormy",
    "Mannfield_Night",
    "ChampionsField_Day",
    "BeckwithPark_Stormy",
    "BeckwithPark_Midnight",
    "UrbanCentral_Night",
    "UrbanCentral_Dawn",
    "UtopiaColiseum_Dusk",
    "DFHStadium_Snowy",
    "Mannfield_Snowy",
    "UtopiaColiseum_Snowy",
    "Badlands",
    "Badlands_Night",
    "TokyoUnderpass",
    "Arctagon",
    "Pillars",
    "Cosmic",
    "DoubleGoal",
    "Octagon",
    "Underpass",
    "UtopiaRetro",
    "Hoops_DunkHouse",
    "DropShot_Core707",
    "ThrowbackStadium",
    "ForbiddenTemple",
    "RivalsArena",
    "Farmstead_Night",
    "SaltyShores_Night",
    "NeonFields",
    nullptr
  };
  return names;
}

inline const char *EnumNameGameMap(GameMap e) {
  const size_t index = static_cast<int>(e);
  return EnumNamesGameMap()[index];
}

enum MatchLength {
  MatchLength_Five_Minutes = 0,
  MatchLength_Ten_Minutes = 1,
  MatchLength_Twenty_Minutes = 2,
  MatchLength_Unlimited = 3,
  MatchLength_MIN = MatchLength_Five_Minutes,
  MatchLength_MAX = MatchLength_Unlimited
};

inline const MatchLength (&EnumValuesMatchLength())[4] {
  static const MatchLength values[] = {
    MatchLength_Five_Minutes,
    MatchLength_Ten_Minutes,
    MatchLength_Twenty_Minutes,
    MatchLength_Unlimited
  };
  return values;
}

inline const char * const *EnumNamesMatchLength() {
  static const char * const names[] = {
    "Five_Minutes",
    "Ten_Minutes",
    "Twenty_Minutes",
    "Unlimited",
    nullptr
  };
  return names;
}

inline const char *EnumNameMatchLength(MatchLength e) {
  const size_t index = static_cast<int>(e);
  return EnumNamesMatchLength()[index];
}

enum MaxScore {
  MaxScore_Unlimited = 0,
  MaxScore_One_Goal = 1,
  MaxScore_Three_Goals = 2,
  MaxScore_Five_Goals = 3,
  MaxScore_MIN = MaxScore_Unlimited,
  MaxScore_MAX = MaxScore_Five_Goals
};

inline const MaxScore (&EnumValuesMaxScore())[4] {
  static const MaxScore values[] = {
    MaxScore_Unlimited,
    MaxScore_One_Goal,
    MaxScore_Three_Goals,
    MaxScore_Five_Goals
  };
  return values;
}

inline const char * const *EnumNamesMaxScore() {
  static const char * const names[] = {
    "Unlimited",
    "One_Goal",
    "Three_Goals",
    "Five_Goals",
    nullptr
  };
  return names;
}

inline const char *EnumNameMaxScore(MaxScore e) {
  const size_t index = static_cast<int>(e);
  return EnumNamesMaxScore()[index];
}

enum OvertimeOption {
  OvertimeOption_Unlimited = 0,
  OvertimeOption_Five_Max_First_Score = 1,
  OvertimeOption_Five_Max_Random_Team = 2,
  OvertimeOption_MIN = OvertimeOption_Unlimited,
  OvertimeOption_MAX = OvertimeOption_Five_Max_Random_Team
};

inline const OvertimeOption (&EnumValuesOvertimeOption())[3] {
  static const OvertimeOption values[] = {
    OvertimeOption_Unlimited,
    OvertimeOption_Five_Max_First_Score,
    OvertimeOption_Five_Max_Random_Team
  };
  return values;
}

inline const char * const *EnumNamesOvertimeOption() {
  static const char * const names[] = {
    "Unlimited",
    "Five_Max_First_Score",
    "Five_Max_Random_Team",
    nullptr
  };
  return names;
}

inline const char *EnumNameOvertimeOption(OvertimeOption e) {
  const size_t index = static_cast<int>(e);
  return EnumNamesOvertimeOption()[index];
}

enum SeriesLengthOption {
  SeriesLengthOption_Unlimited = 0,
  SeriesLengthOption_Three_Games = 1,
  SeriesLengthOption_Five_Games = 2,
  SeriesLengthOption_Seven_Games = 3,
  SeriesLengthOption_MIN = SeriesLengthOption_Unlimited,
  SeriesLengthOption_MAX = SeriesLengthOption_Seven_Games
};

inline const SeriesLengthOption (&EnumValuesSeriesLengthOption())[4] {
  static const SeriesLengthOption values[] = {
    SeriesLengthOption_Unlimited,
    SeriesLengthOption_Three_Games,
    SeriesLengthOption_Five_Games,
    SeriesLengthOption_Seven_Games
  };
  return values;
}

inline const char * const *EnumNamesSeriesLengthOption() {
  static const char * const names[] = {
    "Unlimited",
    "Three_Games",
    "Five_Games",
    "Seven_Games",
    nullptr
  };
  return names;
}

inline const char *EnumNameSeriesLengthOption(SeriesLengthOption e) {
  const size_t index = static_cast<int>(e);
  return EnumNamesSeriesLengthOption()[index];
}

enum GameSpeedOption {
  GameSpeedOption_Default = 0,
  GameSpeedOption_Slo_Mo = 1,
  GameSpeedOption_Time_Warp = 2,
  GameSpeedOption_MIN = GameSpeedOption_Default,
  GameSpeedOption_MAX = GameSpeedOption_Time_Warp
};

inline const GameSpeedOption (&EnumValuesGameSpeedOption())[3] {
  static const GameSpeedOption values[] = {
    GameSpeedOption_Default,
    GameSpeedOption_Slo_Mo,
    GameSpeedOption_Time_Warp
  };
  return values;
}

inline const char * const *EnumNamesGameSpeedOption() {
  static const char * const names[] = {
    "Default",
    "Slo_Mo",
    "Time_Warp",
    nullptr
  };
  return names;
}

inline const char *EnumNameGameSpeedOption(GameSpeedOption e) {
  const size_t index = static_cast<int>(e);
  return EnumNamesGameSpeedOption()[index];
}

enum BallMaxSpeedOption {
  BallMaxSpeedOption_Default = 0,
  BallMaxSpeedOption_Slow = 1,
  BallMaxSpeedOption_Fast = 2,
  BallMaxSpeedOption_Super_Fast = 3,
  BallMaxSpeedOption_MIN = BallMaxSpeedOption_Default,
  BallMaxSpeedOption_MAX = BallMaxSpeedOption_Super_Fast
};

inline const BallMaxSpeedOption (&EnumValuesBallMaxSpeedOption())[4] {
  static const BallMaxSpeedOption values[] = {
    BallMaxSpeedOption_Default,
    BallMaxSpeedOption_Slow,
    BallMaxSpeedOption_Fast,
    BallMaxSpeedOption_Super_Fast
  };
  return values;
}

inline const char * const *EnumNamesBallMaxSpeedOption() {
  static const char * const names[] = {
    "Default",
    "Slow",
    "Fast",
    "Super_Fast",
    nullptr
  };
  return names;
}

inline const char *EnumNameBallMaxSpeedOption(BallMaxSpeedOption e) {
  const size_t index = static_cast<int>(e);
  return EnumNamesBallMaxSpeedOption()[index];
}

enum BallTypeOption {
  BallTypeOption_Default = 0,
  BallTypeOption_Cube = 1,
  BallTypeOption_Puck = 2,
  BallTypeOption_Basketball = 3,
  BallTypeOption_MIN = BallTypeOption_Default,
  BallTypeOption_MAX = BallTypeOption_Basketball
};

inline const BallTypeOption (&EnumValuesBallTypeOption())[4] {
  static const BallTypeOption values[] = {
    BallTypeOption_Default,
    BallTypeOption_Cube,
    BallTypeOption_Puck,
    BallTypeOption_Basketball
  };
  return values;
}

inline const char * const *EnumNamesBallTypeOption() {
  static const char * const names[] = {
    "Default",
    "Cube",
    "Puck",
    "Basketball",
    nullptr
  };
  return names;
}

inline const char *EnumNameBallTypeOption(BallTypeOption e) {
  const size_t index = static_cast<int>(e);
  return EnumNamesBallTypeOption()[index];
}

enum BallWeightOption {
  BallWeightOption_Default = 0,
  BallWeightOption_Light = 1,
  BallWeightOption_Heavy = 2,
  BallWeightOption_Super_Light = 3,
  BallWeightOption_MIN = BallWeightOption_Default,
  BallWeightOption_MAX = BallWeightOption_Super_Light
};

inline const BallWeightOption (&EnumValuesBallWeightOption())[4] {
  static const BallWeightOption values[] = {
    BallWeightOption_Default,
    BallWeightOption_Light,
    BallWeightOption_Heavy,
    BallWeightOption_Super_Light
  };
  return values;
}

inline const char * const *EnumNamesBallWeightOption() {
  static const char * const names[] = {
    "Default",
    "Light",
    "Heavy",
    "Super_Light",
    nullptr
  };
  return names;
}

inline const char *EnumNameBallWeightOption(BallWeightOption e) {
  const size_t index = static_cast<int>(e);
  return EnumNamesBallWeightOption()[index];
}

enum BallSizeOption {
  BallSizeOption_Default = 0,
  BallSizeOption_Small = 1,
  BallSizeOption_Large = 2,
  BallSizeOption_Gigantic = 3,
  BallSizeOption_MIN = BallSizeOption_Default,
  BallSizeOption_MAX = BallSizeOption_Gigantic
};

inline const BallSizeOption (&EnumValuesBallSizeOption())[4] {
  static const BallSizeOption values[] = {
    BallSizeOption_Default,
    BallSizeOption_Small,
    BallSizeOption_Large,
    BallSizeOption_Gigantic
  };
  return values;
}

inline const char * const *EnumNamesBallSizeOption() {
  static const char * const names[] = {
    "Default",
    "Small",
    "Large",
    "Gigantic",
    nullptr
  };
  return names;
}

inline const char *EnumNameBallSizeOption(BallSizeOption e) {
  const size_t index = static_cast<int>(e);
  return EnumNamesBallSizeOption()[index];
}

enum BallBouncinessOption {
  BallBouncinessOption_Default = 0,
  BallBouncinessOption_Low = 1,
  BallBouncinessOption_High = 2,
  BallBouncinessOption_Super_High = 3,
  BallBouncinessOption_MIN = BallBouncinessOption_Default,
  BallBouncinessOption_MAX = BallBouncinessOption_Super_High
};

inline const BallBouncinessOption (&EnumValuesBallBouncinessOption())[4] {
  static const BallBouncinessOption values[] = {
    BallBouncinessOption_Default,
    BallBouncinessOption_Low,
    BallBouncinessOption_High,
    BallBouncinessOption_Super_High
  };
  return values;
}

inline const char * const *EnumNamesBallBouncinessOption() {
  static const char * const names[] = {
    "Default",
    "Low",
    "High",
    "Super_High",
    nullptr
  };
  return names;
}

inline const char *EnumNameBallBouncinessOption(BallBouncinessOption e) {
  const size_t index = static_cast<int>(e);
  return EnumNamesBallBouncinessOption()[index];
}

enum BoostOption {
  BoostOption_Normal_Boost = 0,
  BoostOption_Unlimited_Boost = 1,
  BoostOption_Slow_Recharge = 2,
  BoostOption_Rapid_Recharge = 3,
  BoostOption_No_Boost = 4,
  BoostOption_MIN = BoostOption_Normal_Boost,
  BoostOption_MAX = BoostOption_No_Boost
};

inline const BoostOption (&EnumValuesBoostOption())[5] {
  static const BoostOption values[] = {
    BoostOption_Normal_Boost,
    BoostOption_Unlimited_Boost,
    BoostOption_Slow_Recharge,
    BoostOption_Rapid_Recharge,
    BoostOption_No_Boost
  };
  return values;
}

inline const char * const *EnumNamesBoostOption() {
  static const char * const names[] = {
    "Normal_Boost",
    "Unlimited_Boost",
    "Slow_Recharge",
    "Rapid_Recharge",
    "No_Boost",
    nullptr
  };
  return names;
}

inline const char *EnumNameBoostOption(BoostOption e) {
  const size_t index = static_cast<int>(e);
  return EnumNamesBoostOption()[index];
}

enum RumbleOption {
  RumbleOption_No_Rumble = 0,
  RumbleOption_Default = 1,
  RumbleOption_Slow = 2,
  RumbleOption_Civilized = 3,
  RumbleOption_Destruction_Derby = 4,
  RumbleOption_Spring_Loaded = 5,
  RumbleOption_Spikes_Only = 6,
  RumbleOption_Spike_Rush = 7,
  RumbleOption_MIN = RumbleOption_No_Rumble,
  RumbleOption_MAX = RumbleOption_Spike_Rush
};

inline const RumbleOption (&EnumValuesRumbleOption())[8] {
  static const RumbleOption values[] = {
    RumbleOption_No_Rumble,
    RumbleOption_Default,
    RumbleOption_Slow,
    RumbleOption_Civilized,
    RumbleOption_Destruction_Derby,
    RumbleOption_Spring_Loaded,
    RumbleOption_Spikes_Only,
    RumbleOption_Spike_Rush
  };
  return values;
}

inline const char * const *EnumNamesRumbleOption() {
  static const char * const names[] = {
    "No_Rumble",
    "Default",
    "Slow",
    "Civilized",
    "Destruction_Derby",
    "Spring_Loaded",
    "Spikes_Only",
    "Spike_Rush",
    nullptr
  };
  return names;
}

inline const char *EnumNameRumbleOption(RumbleOption e) {
  const size_t index = static_cast<int>(e);
  return EnumNamesRumbleOption()[index];
}

enum BoostStrengthOption {
  BoostStrengthOption_One = 0,
  BoostStrengthOption_OneAndAHalf = 1,
  BoostStrengthOption_Two = 2,
  BoostStrengthOption_Ten = 3,
  BoostStrengthOption_MIN = BoostStrengthOption_One,
  BoostStrengthOption_MAX = BoostStrengthOption_Ten
};

inline const BoostStrengthOption (&EnumValuesBoostStrengthOption())[4] {
  static const BoostStrengthOption values[] = {
    BoostStrengthOption_One,
    BoostStrengthOption_OneAndAHalf,
    BoostStrengthOption_Two,
    BoostStrengthOption_Ten
  };
  return values;
}

inline const char * const *EnumNamesBoostStrengthOption() {
  static const char * const names[] = {
    "One",
    "OneAndAHalf",
    "Two",
    "Ten",
    nullptr
  };
  return names;
}

inline const char *EnumNameBoostStrengthOption(BoostStrengthOption e) {
  const size_t index = static_cast<int>(e);
  return EnumNamesBoostStrengthOption()[index];
}

enum GravityOption {
  GravityOption_Default = 0,
  GravityOption_Low = 1,
  GravityOption_High = 2,
  GravityOption_Super_High = 3,
  GravityOption_MIN = GravityOption_Default,
  GravityOption_MAX = GravityOption_Super_High
};

inline const GravityOption (&EnumValuesGravityOption())[4] {
  static const GravityOption values[] = {
    GravityOption_Default,
    GravityOption_Low,
    GravityOption_High,
    GravityOption_Super_High
  };
  return values;
}

inline const char * const *EnumNamesGravityOption() {
  static const char * const names[] = {
    "Default",
    "Low",
    "High",
    "Super_High",
    nullptr
  };
  return names;
}

inline const char *EnumNameGravityOption(GravityOption e) {
  const size_t index = static_cast<int>(e);
  return EnumNamesGravityOption()[index];
}

enum DemolishOption {
  DemolishOption_Default = 0,
  DemolishOption_Disabled = 1,
  DemolishOption_Friendly_Fire = 2,
  DemolishOption_On_Contact = 3,
  DemolishOption_On_Contact_FF = 4,
  DemolishOption_MIN = DemolishOption_Default,
  DemolishOption_MAX = DemolishOption_On_Contact_FF
};

inline const DemolishOption (&EnumValuesDemolishOption())[5] {
  static const DemolishOption values[] = {
    DemolishOption_Default,
    DemolishOption_Disabled,
    DemolishOption_Friendly_Fire,
    DemolishOption_On_Contact,
    DemolishOption_On_Contact_FF
  };
  return values;
}

inline const char * const *EnumNamesDemolishOption() {
  static const char * const names[] = {
    "Default",
    "Disabled",
    "Friendly_Fire",
    "On_Contact",
    "On_Contact_FF",
    nullptr
  };
  return names;
}

inline const char *EnumNameDemolishOption(DemolishOption e) {
  const size_t index = static_cast<int>(e);
  return EnumNamesDemolishOption()[index];
}

enum RespawnTimeOption {
  RespawnTimeOption_Three_Seconds = 0,
  RespawnTimeOption_Two_Seconds = 1,
  RespawnTimeOption_One_Seconds = 2,
  RespawnTimeOption_Disable_Goal_Reset = 3,
  RespawnTimeOption_MIN = RespawnTimeOption_Three_Seconds,
  RespawnTimeOption_MAX = RespawnTimeOption_Disable_Goal_Reset
};

inline const RespawnTimeOption (&EnumValuesRespawnTimeOption())[4] {
  static const RespawnTimeOption values[] = {
    RespawnTimeOption_Three_Seconds,
    RespawnTimeOption_Two_Seconds,
    RespawnTimeOption_One_Seconds,
    RespawnTimeOption_Disable_Goal_Reset
  };
  return values;
}

inline const char * const *EnumNamesRespawnTimeOption() {
  static const char * const names[] = {
    "Three_Seconds",
    "Two_Seconds",
    "One_Seconds",
    "Disable_Goal_Reset",
    nullptr
  };
  return names;
}

inline const char *EnumNameRespawnTimeOption(RespawnTimeOption e) {
  const size_t index = static_cast<int>(e);
  return EnumNamesRespawnTimeOption()[index];
}

enum ExistingMatchBehavior {
  /// Restart the match if any match settings differ. This is the default because old RLBot always worked this way.
  ExistingMatchBehavior_Restart_If_Different = 0  /// Always restart the match, even if config is identical
,
  ExistingMatchBehavior_Restart = 1  /// Never restart an existing match, just try to remove or spawn cars to match the configuration.
  /// If we are not in the middle of a match, a match will be started. Handy for LAN matches.
,
  ExistingMatchBehavior_Continue_And_Spawn = 2,
  ExistingMatchBehavior_MIN = ExistingMatchBehavior_Restart_If_Different,
  ExistingMatchBehavior_MAX = ExistingMatchBehavior_Continue_And_Spawn
};

inline const ExistingMatchBehavior (&EnumValuesExistingMatchBehavior())[3] {
  static const ExistingMatchBehavior values[] = {
    ExistingMatchBehavior_Restart_If_Different,
    ExistingMatchBehavior_Restart,
    ExistingMatchBehavior_Continue_And_Spawn
  };
  return values;
}

inline const char * const *EnumNamesExistingMatchBehavior() {
  static const char * const names[] = {
    "Restart_If_Different",
    "Restart",
    "Continue_And_Spawn",
    nullptr
  };
  return names;
}

inline const char *EnumNameExistingMatchBehavior(ExistingMatchBehavior e) {
  const size_t index = static_cast<int>(e);
  return EnumNamesExistingMatchBehavior()[index];
}

enum GameMessage {
  GameMessage_NONE = 0,
  GameMessage_PlayerStatEvent = 1,
  GameMessage_PlayerSpectate = 2,
  GameMessage_PlayerInputChange = 3,
  GameMessage_MIN = GameMessage_NONE,
  GameMessage_MAX = GameMessage_PlayerInputChange
};

inline const GameMessage (&EnumValuesGameMessage())[4] {
  static const GameMessage values[] = {
    GameMessage_NONE,
    GameMessage_PlayerStatEvent,
    GameMessage_PlayerSpectate,
    GameMessage_PlayerInputChange
  };
  return values;
}

inline const char * const *EnumNamesGameMessage() {
  static const char * const names[] = {
    "NONE",
    "PlayerStatEvent",
    "PlayerSpectate",
    "PlayerInputChange",
    nullptr
  };
  return names;
}

inline const char *EnumNameGameMessage(GameMessage e) {
  const size_t index = static_cast<int>(e);
  return EnumNamesGameMessage()[index];
}

template<typename T> struct GameMessageTraits {
  static const GameMessage enum_value = GameMessage_NONE;
};

template<> struct GameMessageTraits<PlayerStatEvent> {
  static const GameMessage enum_value = GameMessage_PlayerStatEvent;
};

template<> struct GameMessageTraits<PlayerSpectate> {
  static const GameMessage enum_value = GameMessage_PlayerSpectate;
};

template<> struct GameMessageTraits<PlayerInputChange> {
  static const GameMessage enum_value = GameMessage_PlayerInputChange;
};

bool VerifyGameMessage(flatbuffers::Verifier &verifier, const void *obj, GameMessage type);
bool VerifyGameMessageVector(flatbuffers::Verifier &verifier, const flatbuffers::Vector<flatbuffers::Offset<void>> *values, const flatbuffers::Vector<uint8_t> *types);

MANUALLY_ALIGNED_STRUCT(4) Vector3 FLATBUFFERS_FINAL_CLASS {
 private:
  float x_;
  float y_;
  float z_;

 public:
  Vector3() {
    memset(this, 0, sizeof(Vector3));
  }
  Vector3(float _x, float _y, float _z)
      : x_(flatbuffers::EndianScalar(_x)),
        y_(flatbuffers::EndianScalar(_y)),
        z_(flatbuffers::EndianScalar(_z)) {
  }
  float x() const {
    return flatbuffers::EndianScalar(x_);
  }
  float y() const {
    return flatbuffers::EndianScalar(y_);
  }
  float z() const {
    return flatbuffers::EndianScalar(z_);
  }
};
STRUCT_END(Vector3, 12);

/// Expresses the rotation state of an object in Euler angles, with values in radians.
MANUALLY_ALIGNED_STRUCT(4) Rotator FLATBUFFERS_FINAL_CLASS {
 private:
  float pitch_;
  float yaw_;
  float roll_;

 public:
  Rotator() {
    memset(this, 0, sizeof(Rotator));
  }
  Rotator(float _pitch, float _yaw, float _roll)
      : pitch_(flatbuffers::EndianScalar(_pitch)),
        yaw_(flatbuffers::EndianScalar(_yaw)),
        roll_(flatbuffers::EndianScalar(_roll)) {
  }
  float pitch() const {
    return flatbuffers::EndianScalar(pitch_);
  }
  float yaw() const {
    return flatbuffers::EndianScalar(yaw_);
  }
  float roll() const {
    return flatbuffers::EndianScalar(roll_);
  }
};
STRUCT_END(Rotator, 12);

/// Expresses the rotation state of an object.
/// Learn about quaternions here: https://en.wikipedia.org/wiki/Quaternions_and_spatial_rotation
/// You can tinker with them here to build an intuition: https://quaternions.online/
MANUALLY_ALIGNED_STRUCT(4) Quaternion FLATBUFFERS_FINAL_CLASS {
 private:
  float x_;
  float y_;
  float z_;
  float w_;

 public:
  Quaternion() {
    memset(this, 0, sizeof(Quaternion));
  }
  Quaternion(float _x, float _y, float _z, float _w)
      : x_(flatbuffers::EndianScalar(_x)),
        y_(flatbuffers::EndianScalar(_y)),
        z_(flatbuffers::EndianScalar(_z)),
        w_(flatbuffers::EndianScalar(_w)) {
  }
  float x() const {
    return flatbuffers::EndianScalar(x_);
  }
  float y() const {
    return flatbuffers::EndianScalar(y_);
  }
  float z() const {
    return flatbuffers::EndianScalar(z_);
  }
  float w() const {
    return flatbuffers::EndianScalar(w_);
  }
};
STRUCT_END(Quaternion, 16);

MANUALLY_ALIGNED_STRUCT(4) Float FLATBUFFERS_FINAL_CLASS {
 private:
  float val_;

 public:
  Float() {
    memset(this, 0, sizeof(Float));
  }
  Float(float _val)
      : val_(flatbuffers::EndianScalar(_val)) {
  }
  float val() const {
    return flatbuffers::EndianScalar(val_);
  }
};
STRUCT_END(Float, 4);

MANUALLY_ALIGNED_STRUCT(1) Bool FLATBUFFERS_FINAL_CLASS {
 private:
  uint8_t val_;

 public:
  Bool() {
    memset(this, 0, sizeof(Bool));
  }
  Bool(bool _val)
      : val_(flatbuffers::EndianScalar(static_cast<uint8_t>(_val))) {
  }
  bool val() const {
    return flatbuffers::EndianScalar(val_) != 0;
  }
};
STRUCT_END(Bool, 1);

struct ControllerState FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_THROTTLE = 4,
    VT_STEER = 6,
    VT_PITCH = 8,
    VT_YAW = 10,
    VT_ROLL = 12,
    VT_JUMP = 14,
    VT_BOOST = 16,
    VT_HANDBRAKE = 18,
    VT_USEITEM = 20
  };
  /// -1 for full reverse, 1 for full forward
  float throttle() const {
    return GetField<float>(VT_THROTTLE, 0.0f);
  }
  /// -1 for full left, 1 for full right
  float steer() const {
    return GetField<float>(VT_STEER, 0.0f);
  }
  /// -1 for nose down, 1 for nose up
  float pitch() const {
    return GetField<float>(VT_PITCH, 0.0f);
  }
  /// -1 for full left, 1 for full right
  float yaw() const {
    return GetField<float>(VT_YAW, 0.0f);
  }
  /// -1 for roll left, 1 for roll right
  float roll() const {
    return GetField<float>(VT_ROLL, 0.0f);
  }
  /// true if you want to press the jump button
  bool jump() const {
    return GetField<uint8_t>(VT_JUMP, 0) != 0;
  }
  /// true if you want to press the boost button
  bool boost() const {
    return GetField<uint8_t>(VT_BOOST, 0) != 0;
  }
  /// true if you want to press the handbrake button
  bool handbrake() const {
    return GetField<uint8_t>(VT_HANDBRAKE, 0) != 0;
  }
  /// true if you want to press the 'use item' button, used in rumble etc.
  bool useItem() const {
    return GetField<uint8_t>(VT_USEITEM, 0) != 0;
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<float>(verifier, VT_THROTTLE) &&
           VerifyField<float>(verifier, VT_STEER) &&
           VerifyField<float>(verifier, VT_PITCH) &&
           VerifyField<float>(verifier, VT_YAW) &&
           VerifyField<float>(verifier, VT_ROLL) &&
           VerifyField<uint8_t>(verifier, VT_JUMP) &&
           VerifyField<uint8_t>(verifier, VT_BOOST) &&
           VerifyField<uint8_t>(verifier, VT_HANDBRAKE) &&
           VerifyField<uint8_t>(verifier, VT_USEITEM) &&
           verifier.EndTable();
  }
};

struct ControllerStateBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_throttle(float throttle) {
    fbb_.AddElement<float>(ControllerState::VT_THROTTLE, throttle, 0.0f);
  }
  void add_steer(float steer) {
    fbb_.AddElement<float>(ControllerState::VT_STEER, steer, 0.0f);
  }
  void add_pitch(float pitch) {
    fbb_.AddElement<float>(ControllerState::VT_PITCH, pitch, 0.0f);
  }
  void add_yaw(float yaw) {
    fbb_.AddElement<float>(ControllerState::VT_YAW, yaw, 0.0f);
  }
  void add_roll(float roll) {
    fbb_.AddElement<float>(ControllerState::VT_ROLL, roll, 0.0f);
  }
  void add_jump(bool jump) {
    fbb_.AddElement<uint8_t>(ControllerState::VT_JUMP, static_cast<uint8_t>(jump), 0);
  }
  void add_boost(bool boost) {
    fbb_.AddElement<uint8_t>(ControllerState::VT_BOOST, static_cast<uint8_t>(boost), 0);
  }
  void add_handbrake(bool handbrake) {
    fbb_.AddElement<uint8_t>(ControllerState::VT_HANDBRAKE, static_cast<uint8_t>(handbrake), 0);
  }
  void add_useItem(bool useItem) {
    fbb_.AddElement<uint8_t>(ControllerState::VT_USEITEM, static_cast<uint8_t>(useItem), 0);
  }
  explicit ControllerStateBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ControllerStateBuilder &operator=(const ControllerStateBuilder &);
  flatbuffers::Offset<ControllerState> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<ControllerState>(end);
    return o;
  }
};

inline flatbuffers::Offset<ControllerState> CreateControllerState(
    flatbuffers::FlatBufferBuilder &_fbb,
    float throttle = 0.0f,
    float steer = 0.0f,
    float pitch = 0.0f,
    float yaw = 0.0f,
    float roll = 0.0f,
    bool jump = false,
    bool boost = false,
    bool handbrake = false,
    bool useItem = false) {
  ControllerStateBuilder builder_(_fbb);
  builder_.add_roll(roll);
  builder_.add_yaw(yaw);
  builder_.add_pitch(pitch);
  builder_.add_steer(steer);
  builder_.add_throttle(throttle);
  builder_.add_useItem(useItem);
  builder_.add_handbrake(handbrake);
  builder_.add_boost(boost);
  builder_.add_jump(jump);
  return builder_.Finish();
}

struct PlayerInput FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_PLAYERINDEX = 4,
    VT_CONTROLLERSTATE = 6
  };
  int32_t playerIndex() const {
    return GetField<int32_t>(VT_PLAYERINDEX, 0);
  }
  const ControllerState *controllerState() const {
    return GetPointer<const ControllerState *>(VT_CONTROLLERSTATE);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_PLAYERINDEX) &&
           VerifyOffset(verifier, VT_CONTROLLERSTATE) &&
           verifier.VerifyTable(controllerState()) &&
           verifier.EndTable();
  }
};

struct PlayerInputBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_playerIndex(int32_t playerIndex) {
    fbb_.AddElement<int32_t>(PlayerInput::VT_PLAYERINDEX, playerIndex, 0);
  }
  void add_controllerState(flatbuffers::Offset<ControllerState> controllerState) {
    fbb_.AddOffset(PlayerInput::VT_CONTROLLERSTATE, controllerState);
  }
  explicit PlayerInputBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  PlayerInputBuilder &operator=(const PlayerInputBuilder &);
  flatbuffers::Offset<PlayerInput> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<PlayerInput>(end);
    return o;
  }
};

inline flatbuffers::Offset<PlayerInput> CreatePlayerInput(
    flatbuffers::FlatBufferBuilder &_fbb,
    int32_t playerIndex = 0,
    flatbuffers::Offset<ControllerState> controllerState = 0) {
  PlayerInputBuilder builder_(_fbb);
  builder_.add_controllerState(controllerState);
  builder_.add_playerIndex(playerIndex);
  return builder_.Finish();
}

struct BoxShape FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_LENGTH = 4,
    VT_WIDTH = 6,
    VT_HEIGHT = 8
  };
  float length() const {
    return GetField<float>(VT_LENGTH, 0.0f);
  }
  float width() const {
    return GetField<float>(VT_WIDTH, 0.0f);
  }
  float height() const {
    return GetField<float>(VT_HEIGHT, 0.0f);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<float>(verifier, VT_LENGTH) &&
           VerifyField<float>(verifier, VT_WIDTH) &&
           VerifyField<float>(verifier, VT_HEIGHT) &&
           verifier.EndTable();
  }
};

struct BoxShapeBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_length(float length) {
    fbb_.AddElement<float>(BoxShape::VT_LENGTH, length, 0.0f);
  }
  void add_width(float width) {
    fbb_.AddElement<float>(BoxShape::VT_WIDTH, width, 0.0f);
  }
  void add_height(float height) {
    fbb_.AddElement<float>(BoxShape::VT_HEIGHT, height, 0.0f);
  }
  explicit BoxShapeBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  BoxShapeBuilder &operator=(const BoxShapeBuilder &);
  flatbuffers::Offset<BoxShape> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<BoxShape>(end);
    return o;
  }
};

inline flatbuffers::Offset<BoxShape> CreateBoxShape(
    flatbuffers::FlatBufferBuilder &_fbb,
    float length = 0.0f,
    float width = 0.0f,
    float height = 0.0f) {
  BoxShapeBuilder builder_(_fbb);
  builder_.add_height(height);
  builder_.add_width(width);
  builder_.add_length(length);
  return builder_.Finish();
}

struct SphereShape FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_DIAMETER = 4
  };
  float diameter() const {
    return GetField<float>(VT_DIAMETER, 0.0f);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<float>(verifier, VT_DIAMETER) &&
           verifier.EndTable();
  }
};

struct SphereShapeBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_diameter(float diameter) {
    fbb_.AddElement<float>(SphereShape::VT_DIAMETER, diameter, 0.0f);
  }
  explicit SphereShapeBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  SphereShapeBuilder &operator=(const SphereShapeBuilder &);
  flatbuffers::Offset<SphereShape> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<SphereShape>(end);
    return o;
  }
};

inline flatbuffers::Offset<SphereShape> CreateSphereShape(
    flatbuffers::FlatBufferBuilder &_fbb,
    float diameter = 0.0f) {
  SphereShapeBuilder builder_(_fbb);
  builder_.add_diameter(diameter);
  return builder_.Finish();
}

struct CylinderShape FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_DIAMETER = 4,
    VT_HEIGHT = 6
  };
  float diameter() const {
    return GetField<float>(VT_DIAMETER, 0.0f);
  }
  float height() const {
    return GetField<float>(VT_HEIGHT, 0.0f);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<float>(verifier, VT_DIAMETER) &&
           VerifyField<float>(verifier, VT_HEIGHT) &&
           verifier.EndTable();
  }
};

struct CylinderShapeBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_diameter(float diameter) {
    fbb_.AddElement<float>(CylinderShape::VT_DIAMETER, diameter, 0.0f);
  }
  void add_height(float height) {
    fbb_.AddElement<float>(CylinderShape::VT_HEIGHT, height, 0.0f);
  }
  explicit CylinderShapeBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  CylinderShapeBuilder &operator=(const CylinderShapeBuilder &);
  flatbuffers::Offset<CylinderShape> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<CylinderShape>(end);
    return o;
  }
};

inline flatbuffers::Offset<CylinderShape> CreateCylinderShape(
    flatbuffers::FlatBufferBuilder &_fbb,
    float diameter = 0.0f,
    float height = 0.0f) {
  CylinderShapeBuilder builder_(_fbb);
  builder_.add_height(height);
  builder_.add_diameter(diameter);
  return builder_.Finish();
}

struct Touch FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_PLAYERNAME = 4,
    VT_GAMESECONDS = 6,
    VT_LOCATION = 8,
    VT_NORMAL = 10,
    VT_TEAM = 12,
    VT_PLAYERINDEX = 14
  };
  /// The name of the player involved with the touch.
  const flatbuffers::String *playerName() const {
    return GetPointer<const flatbuffers::String *>(VT_PLAYERNAME);
  }
  /// Seconds that had elapsed in the game when the touch occurred.
  float gameSeconds() const {
    return GetField<float>(VT_GAMESECONDS, 0.0f);
  }
  /// The point of contact for the touch.
  const Vector3 *location() const {
    return GetStruct<const Vector3 *>(VT_LOCATION);
  }
  /// The direction of the touch.
  const Vector3 *normal() const {
    return GetStruct<const Vector3 *>(VT_NORMAL);
  }
  /// The Team which the touch belongs to, 0 for blue 1 for orange.
  int32_t team() const {
    return GetField<int32_t>(VT_TEAM, 0);
  }
  /// The index of the player involved with the touch.
  int32_t playerIndex() const {
    return GetField<int32_t>(VT_PLAYERINDEX, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PLAYERNAME) &&
           verifier.Verify(playerName()) &&
           VerifyField<float>(verifier, VT_GAMESECONDS) &&
           VerifyField<Vector3>(verifier, VT_LOCATION) &&
           VerifyField<Vector3>(verifier, VT_NORMAL) &&
           VerifyField<int32_t>(verifier, VT_TEAM) &&
           VerifyField<int32_t>(verifier, VT_PLAYERINDEX) &&
           verifier.EndTable();
  }
};

struct TouchBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_playerName(flatbuffers::Offset<flatbuffers::String> playerName) {
    fbb_.AddOffset(Touch::VT_PLAYERNAME, playerName);
  }
  void add_gameSeconds(float gameSeconds) {
    fbb_.AddElement<float>(Touch::VT_GAMESECONDS, gameSeconds, 0.0f);
  }
  void add_location(const Vector3 *location) {
    fbb_.AddStruct(Touch::VT_LOCATION, location);
  }
  void add_normal(const Vector3 *normal) {
    fbb_.AddStruct(Touch::VT_NORMAL, normal);
  }
  void add_team(int32_t team) {
    fbb_.AddElement<int32_t>(Touch::VT_TEAM, team, 0);
  }
  void add_playerIndex(int32_t playerIndex) {
    fbb_.AddElement<int32_t>(Touch::VT_PLAYERINDEX, playerIndex, 0);
  }
  explicit TouchBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  TouchBuilder &operator=(const TouchBuilder &);
  flatbuffers::Offset<Touch> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<Touch>(end);
    return o;
  }
};

inline flatbuffers::Offset<Touch> CreateTouch(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> playerName = 0,
    float gameSeconds = 0.0f,
    const Vector3 *location = 0,
    const Vector3 *normal = 0,
    int32_t team = 0,
    int32_t playerIndex = 0) {
  TouchBuilder builder_(_fbb);
  builder_.add_playerIndex(playerIndex);
  builder_.add_team(team);
  builder_.add_normal(normal);
  builder_.add_location(location);
  builder_.add_gameSeconds(gameSeconds);
  builder_.add_playerName(playerName);
  return builder_.Finish();
}

inline flatbuffers::Offset<Touch> CreateTouchDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *playerName = nullptr,
    float gameSeconds = 0.0f,
    const Vector3 *location = 0,
    const Vector3 *normal = 0,
    int32_t team = 0,
    int32_t playerIndex = 0) {
  return rlbot::flat::CreateTouch(
      _fbb,
      playerName ? _fbb.CreateString(playerName) : 0,
      gameSeconds,
      location,
      normal,
      team,
      playerIndex);
}

struct ScoreInfo FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_SCORE = 4,
    VT_GOALS = 6,
    VT_OWNGOALS = 8,
    VT_ASSISTS = 10,
    VT_SAVES = 12,
    VT_SHOTS = 14,
    VT_DEMOLITIONS = 16
  };
  int32_t score() const {
    return GetField<int32_t>(VT_SCORE, 0);
  }
  int32_t goals() const {
    return GetField<int32_t>(VT_GOALS, 0);
  }
  int32_t ownGoals() const {
    return GetField<int32_t>(VT_OWNGOALS, 0);
  }
  int32_t assists() const {
    return GetField<int32_t>(VT_ASSISTS, 0);
  }
  int32_t saves() const {
    return GetField<int32_t>(VT_SAVES, 0);
  }
  int32_t shots() const {
    return GetField<int32_t>(VT_SHOTS, 0);
  }
  int32_t demolitions() const {
    return GetField<int32_t>(VT_DEMOLITIONS, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_SCORE) &&
           VerifyField<int32_t>(verifier, VT_GOALS) &&
           VerifyField<int32_t>(verifier, VT_OWNGOALS) &&
           VerifyField<int32_t>(verifier, VT_ASSISTS) &&
           VerifyField<int32_t>(verifier, VT_SAVES) &&
           VerifyField<int32_t>(verifier, VT_SHOTS) &&
           VerifyField<int32_t>(verifier, VT_DEMOLITIONS) &&
           verifier.EndTable();
  }
};

struct ScoreInfoBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_score(int32_t score) {
    fbb_.AddElement<int32_t>(ScoreInfo::VT_SCORE, score, 0);
  }
  void add_goals(int32_t goals) {
    fbb_.AddElement<int32_t>(ScoreInfo::VT_GOALS, goals, 0);
  }
  void add_ownGoals(int32_t ownGoals) {
    fbb_.AddElement<int32_t>(ScoreInfo::VT_OWNGOALS, ownGoals, 0);
  }
  void add_assists(int32_t assists) {
    fbb_.AddElement<int32_t>(ScoreInfo::VT_ASSISTS, assists, 0);
  }
  void add_saves(int32_t saves) {
    fbb_.AddElement<int32_t>(ScoreInfo::VT_SAVES, saves, 0);
  }
  void add_shots(int32_t shots) {
    fbb_.AddElement<int32_t>(ScoreInfo::VT_SHOTS, shots, 0);
  }
  void add_demolitions(int32_t demolitions) {
    fbb_.AddElement<int32_t>(ScoreInfo::VT_DEMOLITIONS, demolitions, 0);
  }
  explicit ScoreInfoBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ScoreInfoBuilder &operator=(const ScoreInfoBuilder &);
  flatbuffers::Offset<ScoreInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<ScoreInfo>(end);
    return o;
  }
};

inline flatbuffers::Offset<ScoreInfo> CreateScoreInfo(
    flatbuffers::FlatBufferBuilder &_fbb,
    int32_t score = 0,
    int32_t goals = 0,
    int32_t ownGoals = 0,
    int32_t assists = 0,
    int32_t saves = 0,
    int32_t shots = 0,
    int32_t demolitions = 0) {
  ScoreInfoBuilder builder_(_fbb);
  builder_.add_demolitions(demolitions);
  builder_.add_shots(shots);
  builder_.add_saves(saves);
  builder_.add_assists(assists);
  builder_.add_ownGoals(ownGoals);
  builder_.add_goals(goals);
  builder_.add_score(score);
  return builder_.Finish();
}

struct Physics FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_LOCATION = 4,
    VT_ROTATION = 6,
    VT_VELOCITY = 8,
    VT_ANGULARVELOCITY = 10
  };
  const Vector3 *location() const {
    return GetStruct<const Vector3 *>(VT_LOCATION);
  }
  const Rotator *rotation() const {
    return GetStruct<const Rotator *>(VT_ROTATION);
  }
  const Vector3 *velocity() const {
    return GetStruct<const Vector3 *>(VT_VELOCITY);
  }
  const Vector3 *angularVelocity() const {
    return GetStruct<const Vector3 *>(VT_ANGULARVELOCITY);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<Vector3>(verifier, VT_LOCATION) &&
           VerifyField<Rotator>(verifier, VT_ROTATION) &&
           VerifyField<Vector3>(verifier, VT_VELOCITY) &&
           VerifyField<Vector3>(verifier, VT_ANGULARVELOCITY) &&
           verifier.EndTable();
  }
};

struct PhysicsBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_location(const Vector3 *location) {
    fbb_.AddStruct(Physics::VT_LOCATION, location);
  }
  void add_rotation(const Rotator *rotation) {
    fbb_.AddStruct(Physics::VT_ROTATION, rotation);
  }
  void add_velocity(const Vector3 *velocity) {
    fbb_.AddStruct(Physics::VT_VELOCITY, velocity);
  }
  void add_angularVelocity(const Vector3 *angularVelocity) {
    fbb_.AddStruct(Physics::VT_ANGULARVELOCITY, angularVelocity);
  }
  explicit PhysicsBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  PhysicsBuilder &operator=(const PhysicsBuilder &);
  flatbuffers::Offset<Physics> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<Physics>(end);
    return o;
  }
};

inline flatbuffers::Offset<Physics> CreatePhysics(
    flatbuffers::FlatBufferBuilder &_fbb,
    const Vector3 *location = 0,
    const Rotator *rotation = 0,
    const Vector3 *velocity = 0,
    const Vector3 *angularVelocity = 0) {
  PhysicsBuilder builder_(_fbb);
  builder_.add_angularVelocity(angularVelocity);
  builder_.add_velocity(velocity);
  builder_.add_rotation(rotation);
  builder_.add_location(location);
  return builder_.Finish();
}

struct PlayerInfo FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_PHYSICS = 4,
    VT_SCOREINFO = 6,
    VT_ISDEMOLISHED = 8,
    VT_HASWHEELCONTACT = 10,
    VT_ISSUPERSONIC = 12,
    VT_ISBOT = 14,
    VT_JUMPED = 16,
    VT_DOUBLEJUMPED = 18,
    VT_NAME = 20,
    VT_TEAM = 22,
    VT_BOOST = 24,
    VT_HITBOX = 26,
    VT_HITBOXOFFSET = 28,
    VT_SPAWNID = 30
  };
  const Physics *physics() const {
    return GetPointer<const Physics *>(VT_PHYSICS);
  }
  const ScoreInfo *scoreInfo() const {
    return GetPointer<const ScoreInfo *>(VT_SCOREINFO);
  }
  bool isDemolished() const {
    return GetField<uint8_t>(VT_ISDEMOLISHED, 0) != 0;
  }
  /// True if your wheels are on the ground, the wall, or the ceiling. False if you're midair or turtling.
  bool hasWheelContact() const {
    return GetField<uint8_t>(VT_HASWHEELCONTACT, 0) != 0;
  }
  bool isSupersonic() const {
    return GetField<uint8_t>(VT_ISSUPERSONIC, 0) != 0;
  }
  bool isBot() const {
    return GetField<uint8_t>(VT_ISBOT, 0) != 0;
  }
  /// True if the player has jumped. Falling off the ceiling / driving off the goal post does not count.
  bool jumped() const {
    return GetField<uint8_t>(VT_JUMPED, 0) != 0;
  }
  ///  True if player has double jumped. False does not mean you have a jump remaining, because the
  ///  aerial timer can run out, and that doesn't affect this flag.
  bool doubleJumped() const {
    return GetField<uint8_t>(VT_DOUBLEJUMPED, 0) != 0;
  }
  const flatbuffers::String *name() const {
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  int32_t team() const {
    return GetField<int32_t>(VT_TEAM, 0);
  }
  int32_t boost() const {
    return GetField<int32_t>(VT_BOOST, 0);
  }
  const BoxShape *hitbox() const {
    return GetPointer<const BoxShape *>(VT_HITBOX);
  }
  const Vector3 *hitboxOffset() const {
    return GetStruct<const Vector3 *>(VT_HITBOXOFFSET);
  }
  /// In the case where the requested player index is not available, spawnId will help
  /// the framework figure out what index was actually assigned to this player instead.
  int32_t spawnId() const {
    return GetField<int32_t>(VT_SPAWNID, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PHYSICS) &&
           verifier.VerifyTable(physics()) &&
           VerifyOffset(verifier, VT_SCOREINFO) &&
           verifier.VerifyTable(scoreInfo()) &&
           VerifyField<uint8_t>(verifier, VT_ISDEMOLISHED) &&
           VerifyField<uint8_t>(verifier, VT_HASWHEELCONTACT) &&
           VerifyField<uint8_t>(verifier, VT_ISSUPERSONIC) &&
           VerifyField<uint8_t>(verifier, VT_ISBOT) &&
           VerifyField<uint8_t>(verifier, VT_JUMPED) &&
           VerifyField<uint8_t>(verifier, VT_DOUBLEJUMPED) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.Verify(name()) &&
           VerifyField<int32_t>(verifier, VT_TEAM) &&
           VerifyField<int32_t>(verifier, VT_BOOST) &&
           VerifyOffset(verifier, VT_HITBOX) &&
           verifier.VerifyTable(hitbox()) &&
           VerifyField<Vector3>(verifier, VT_HITBOXOFFSET) &&
           VerifyField<int32_t>(verifier, VT_SPAWNID) &&
           verifier.EndTable();
  }
};

struct PlayerInfoBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_physics(flatbuffers::Offset<Physics> physics) {
    fbb_.AddOffset(PlayerInfo::VT_PHYSICS, physics);
  }
  void add_scoreInfo(flatbuffers::Offset<ScoreInfo> scoreInfo) {
    fbb_.AddOffset(PlayerInfo::VT_SCOREINFO, scoreInfo);
  }
  void add_isDemolished(bool isDemolished) {
    fbb_.AddElement<uint8_t>(PlayerInfo::VT_ISDEMOLISHED, static_cast<uint8_t>(isDemolished), 0);
  }
  void add_hasWheelContact(bool hasWheelContact) {
    fbb_.AddElement<uint8_t>(PlayerInfo::VT_HASWHEELCONTACT, static_cast<uint8_t>(hasWheelContact), 0);
  }
  void add_isSupersonic(bool isSupersonic) {
    fbb_.AddElement<uint8_t>(PlayerInfo::VT_ISSUPERSONIC, static_cast<uint8_t>(isSupersonic), 0);
  }
  void add_isBot(bool isBot) {
    fbb_.AddElement<uint8_t>(PlayerInfo::VT_ISBOT, static_cast<uint8_t>(isBot), 0);
  }
  void add_jumped(bool jumped) {
    fbb_.AddElement<uint8_t>(PlayerInfo::VT_JUMPED, static_cast<uint8_t>(jumped), 0);
  }
  void add_doubleJumped(bool doubleJumped) {
    fbb_.AddElement<uint8_t>(PlayerInfo::VT_DOUBLEJUMPED, static_cast<uint8_t>(doubleJumped), 0);
  }
  void add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(PlayerInfo::VT_NAME, name);
  }
  void add_team(int32_t team) {
    fbb_.AddElement<int32_t>(PlayerInfo::VT_TEAM, team, 0);
  }
  void add_boost(int32_t boost) {
    fbb_.AddElement<int32_t>(PlayerInfo::VT_BOOST, boost, 0);
  }
  void add_hitbox(flatbuffers::Offset<BoxShape> hitbox) {
    fbb_.AddOffset(PlayerInfo::VT_HITBOX, hitbox);
  }
  void add_hitboxOffset(const Vector3 *hitboxOffset) {
    fbb_.AddStruct(PlayerInfo::VT_HITBOXOFFSET, hitboxOffset);
  }
  void add_spawnId(int32_t spawnId) {
    fbb_.AddElement<int32_t>(PlayerInfo::VT_SPAWNID, spawnId, 0);
  }
  explicit PlayerInfoBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  PlayerInfoBuilder &operator=(const PlayerInfoBuilder &);
  flatbuffers::Offset<PlayerInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<PlayerInfo>(end);
    return o;
  }
};

inline flatbuffers::Offset<PlayerInfo> CreatePlayerInfo(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<Physics> physics = 0,
    flatbuffers::Offset<ScoreInfo> scoreInfo = 0,
    bool isDemolished = false,
    bool hasWheelContact = false,
    bool isSupersonic = false,
    bool isBot = false,
    bool jumped = false,
    bool doubleJumped = false,
    flatbuffers::Offset<flatbuffers::String> name = 0,
    int32_t team = 0,
    int32_t boost = 0,
    flatbuffers::Offset<BoxShape> hitbox = 0,
    const Vector3 *hitboxOffset = 0,
    int32_t spawnId = 0) {
  PlayerInfoBuilder builder_(_fbb);
  builder_.add_spawnId(spawnId);
  builder_.add_hitboxOffset(hitboxOffset);
  builder_.add_hitbox(hitbox);
  builder_.add_boost(boost);
  builder_.add_team(team);
  builder_.add_name(name);
  builder_.add_scoreInfo(scoreInfo);
  builder_.add_physics(physics);
  builder_.add_doubleJumped(doubleJumped);
  builder_.add_jumped(jumped);
  builder_.add_isBot(isBot);
  builder_.add_isSupersonic(isSupersonic);
  builder_.add_hasWheelContact(hasWheelContact);
  builder_.add_isDemolished(isDemolished);
  return builder_.Finish();
}

inline flatbuffers::Offset<PlayerInfo> CreatePlayerInfoDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<Physics> physics = 0,
    flatbuffers::Offset<ScoreInfo> scoreInfo = 0,
    bool isDemolished = false,
    bool hasWheelContact = false,
    bool isSupersonic = false,
    bool isBot = false,
    bool jumped = false,
    bool doubleJumped = false,
    const char *name = nullptr,
    int32_t team = 0,
    int32_t boost = 0,
    flatbuffers::Offset<BoxShape> hitbox = 0,
    const Vector3 *hitboxOffset = 0,
    int32_t spawnId = 0) {
  return rlbot::flat::CreatePlayerInfo(
      _fbb,
      physics,
      scoreInfo,
      isDemolished,
      hasWheelContact,
      isSupersonic,
      isBot,
      jumped,
      doubleJumped,
      name ? _fbb.CreateString(name) : 0,
      team,
      boost,
      hitbox,
      hitboxOffset,
      spawnId);
}

struct DropShotBallInfo FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_ABSORBEDFORCE = 4,
    VT_DAMAGEINDEX = 6,
    VT_FORCEACCUMRECENT = 8
  };
  float absorbedForce() const {
    return GetField<float>(VT_ABSORBEDFORCE, 0.0f);
  }
  int32_t damageIndex() const {
    return GetField<int32_t>(VT_DAMAGEINDEX, 0);
  }
  float forceAccumRecent() const {
    return GetField<float>(VT_FORCEACCUMRECENT, 0.0f);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<float>(verifier, VT_ABSORBEDFORCE) &&
           VerifyField<int32_t>(verifier, VT_DAMAGEINDEX) &&
           VerifyField<float>(verifier, VT_FORCEACCUMRECENT) &&
           verifier.EndTable();
  }
};

struct DropShotBallInfoBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_absorbedForce(float absorbedForce) {
    fbb_.AddElement<float>(DropShotBallInfo::VT_ABSORBEDFORCE, absorbedForce, 0.0f);
  }
  void add_damageIndex(int32_t damageIndex) {
    fbb_.AddElement<int32_t>(DropShotBallInfo::VT_DAMAGEINDEX, damageIndex, 0);
  }
  void add_forceAccumRecent(float forceAccumRecent) {
    fbb_.AddElement<float>(DropShotBallInfo::VT_FORCEACCUMRECENT, forceAccumRecent, 0.0f);
  }
  explicit DropShotBallInfoBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  DropShotBallInfoBuilder &operator=(const DropShotBallInfoBuilder &);
  flatbuffers::Offset<DropShotBallInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<DropShotBallInfo>(end);
    return o;
  }
};

inline flatbuffers::Offset<DropShotBallInfo> CreateDropShotBallInfo(
    flatbuffers::FlatBufferBuilder &_fbb,
    float absorbedForce = 0.0f,
    int32_t damageIndex = 0,
    float forceAccumRecent = 0.0f) {
  DropShotBallInfoBuilder builder_(_fbb);
  builder_.add_forceAccumRecent(forceAccumRecent);
  builder_.add_damageIndex(damageIndex);
  builder_.add_absorbedForce(absorbedForce);
  return builder_.Finish();
}

struct BallInfo FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_PHYSICS = 4,
    VT_LATESTTOUCH = 6,
    VT_DROPSHOTINFO = 8,
    VT_SHAPE_TYPE = 10,
    VT_SHAPE = 12
  };
  const Physics *physics() const {
    return GetPointer<const Physics *>(VT_PHYSICS);
  }
  const Touch *latestTouch() const {
    return GetPointer<const Touch *>(VT_LATESTTOUCH);
  }
  const DropShotBallInfo *dropShotInfo() const {
    return GetPointer<const DropShotBallInfo *>(VT_DROPSHOTINFO);
  }
  CollisionShape shape_type() const {
    return static_cast<CollisionShape>(GetField<uint8_t>(VT_SHAPE_TYPE, 0));
  }
  const void *shape() const {
    return GetPointer<const void *>(VT_SHAPE);
  }
  template<typename T> const T *shape_as() const;
  const BoxShape *shape_as_BoxShape() const {
    return shape_type() == CollisionShape_BoxShape ? static_cast<const BoxShape *>(shape()) : nullptr;
  }
  const SphereShape *shape_as_SphereShape() const {
    return shape_type() == CollisionShape_SphereShape ? static_cast<const SphereShape *>(shape()) : nullptr;
  }
  const CylinderShape *shape_as_CylinderShape() const {
    return shape_type() == CollisionShape_CylinderShape ? static_cast<const CylinderShape *>(shape()) : nullptr;
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PHYSICS) &&
           verifier.VerifyTable(physics()) &&
           VerifyOffset(verifier, VT_LATESTTOUCH) &&
           verifier.VerifyTable(latestTouch()) &&
           VerifyOffset(verifier, VT_DROPSHOTINFO) &&
           verifier.VerifyTable(dropShotInfo()) &&
           VerifyField<uint8_t>(verifier, VT_SHAPE_TYPE) &&
           VerifyOffset(verifier, VT_SHAPE) &&
           VerifyCollisionShape(verifier, shape(), shape_type()) &&
           verifier.EndTable();
  }
};

template<> inline const BoxShape *BallInfo::shape_as<BoxShape>() const {
  return shape_as_BoxShape();
}

template<> inline const SphereShape *BallInfo::shape_as<SphereShape>() const {
  return shape_as_SphereShape();
}

template<> inline const CylinderShape *BallInfo::shape_as<CylinderShape>() const {
  return shape_as_CylinderShape();
}

struct BallInfoBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_physics(flatbuffers::Offset<Physics> physics) {
    fbb_.AddOffset(BallInfo::VT_PHYSICS, physics);
  }
  void add_latestTouch(flatbuffers::Offset<Touch> latestTouch) {
    fbb_.AddOffset(BallInfo::VT_LATESTTOUCH, latestTouch);
  }
  void add_dropShotInfo(flatbuffers::Offset<DropShotBallInfo> dropShotInfo) {
    fbb_.AddOffset(BallInfo::VT_DROPSHOTINFO, dropShotInfo);
  }
  void add_shape_type(CollisionShape shape_type) {
    fbb_.AddElement<uint8_t>(BallInfo::VT_SHAPE_TYPE, static_cast<uint8_t>(shape_type), 0);
  }
  void add_shape(flatbuffers::Offset<void> shape) {
    fbb_.AddOffset(BallInfo::VT_SHAPE, shape);
  }
  explicit BallInfoBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  BallInfoBuilder &operator=(const BallInfoBuilder &);
  flatbuffers::Offset<BallInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<BallInfo>(end);
    return o;
  }
};

inline flatbuffers::Offset<BallInfo> CreateBallInfo(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<Physics> physics = 0,
    flatbuffers::Offset<Touch> latestTouch = 0,
    flatbuffers::Offset<DropShotBallInfo> dropShotInfo = 0,
    CollisionShape shape_type = CollisionShape_NONE,
    flatbuffers::Offset<void> shape = 0) {
  BallInfoBuilder builder_(_fbb);
  builder_.add_shape(shape);
  builder_.add_dropShotInfo(dropShotInfo);
  builder_.add_latestTouch(latestTouch);
  builder_.add_physics(physics);
  builder_.add_shape_type(shape_type);
  return builder_.Finish();
}

struct BoostPadState FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_ISACTIVE = 4,
    VT_TIMER = 6
  };
  /// True if the boost can be picked up
  bool isActive() const {
    return GetField<uint8_t>(VT_ISACTIVE, 0) != 0;
  }
  /// The number of seconds since the boost has been picked up, or 0.0 if the boost is active.
  float timer() const {
    return GetField<float>(VT_TIMER, 0.0f);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_ISACTIVE) &&
           VerifyField<float>(verifier, VT_TIMER) &&
           verifier.EndTable();
  }
};

struct BoostPadStateBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_isActive(bool isActive) {
    fbb_.AddElement<uint8_t>(BoostPadState::VT_ISACTIVE, static_cast<uint8_t>(isActive), 0);
  }
  void add_timer(float timer) {
    fbb_.AddElement<float>(BoostPadState::VT_TIMER, timer, 0.0f);
  }
  explicit BoostPadStateBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  BoostPadStateBuilder &operator=(const BoostPadStateBuilder &);
  flatbuffers::Offset<BoostPadState> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<BoostPadState>(end);
    return o;
  }
};

inline flatbuffers::Offset<BoostPadState> CreateBoostPadState(
    flatbuffers::FlatBufferBuilder &_fbb,
    bool isActive = false,
    float timer = 0.0f) {
  BoostPadStateBuilder builder_(_fbb);
  builder_.add_timer(timer);
  builder_.add_isActive(isActive);
  return builder_.Finish();
}

struct DropshotTile FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_TILESTATE = 4
  };
  /// The amount of damage the tile has sustained.
  TileState tileState() const {
    return static_cast<TileState>(GetField<int8_t>(VT_TILESTATE, 0));
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_TILESTATE) &&
           verifier.EndTable();
  }
};

struct DropshotTileBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_tileState(TileState tileState) {
    fbb_.AddElement<int8_t>(DropshotTile::VT_TILESTATE, static_cast<int8_t>(tileState), 0);
  }
  explicit DropshotTileBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  DropshotTileBuilder &operator=(const DropshotTileBuilder &);
  flatbuffers::Offset<DropshotTile> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<DropshotTile>(end);
    return o;
  }
};

inline flatbuffers::Offset<DropshotTile> CreateDropshotTile(
    flatbuffers::FlatBufferBuilder &_fbb,
    TileState tileState = TileState_Unknown) {
  DropshotTileBuilder builder_(_fbb);
  builder_.add_tileState(tileState);
  return builder_.Finish();
}

struct GameInfo FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_SECONDSELAPSED = 4,
    VT_GAMETIMEREMAINING = 6,
    VT_ISOVERTIME = 8,
    VT_ISUNLIMITEDTIME = 10,
    VT_ISROUNDACTIVE = 12,
    VT_ISKICKOFFPAUSE = 14,
    VT_ISMATCHENDED = 16,
    VT_WORLDGRAVITYZ = 18,
    VT_GAMESPEED = 20,
    VT_FRAMENUM = 22
  };
  float secondsElapsed() const {
    return GetField<float>(VT_SECONDSELAPSED, 0.0f);
  }
  float gameTimeRemaining() const {
    return GetField<float>(VT_GAMETIMEREMAINING, 0.0f);
  }
  bool isOvertime() const {
    return GetField<uint8_t>(VT_ISOVERTIME, 0) != 0;
  }
  bool isUnlimitedTime() const {
    return GetField<uint8_t>(VT_ISUNLIMITEDTIME, 0) != 0;
  }
  /// True when cars are allowed to move, and during the pause menu. False during replays.
  bool isRoundActive() const {
    return GetField<uint8_t>(VT_ISROUNDACTIVE, 0) != 0;
  }
  /// True when the clock is paused due to kickoff, but false during kickoff countdown. In other words, it is true
  /// while cars can move during kickoff. Note that if both players sit still, game clock start and this will become false.
  bool isKickoffPause() const {
    return GetField<uint8_t>(VT_ISKICKOFFPAUSE, 0) != 0;
  }
  /// Turns true after final replay, the moment the 'winner' screen appears. Remains true during next match
  /// countdown. Turns false again the moment the 'choose team' screen appears.
  bool isMatchEnded() const {
    return GetField<uint8_t>(VT_ISMATCHENDED, 0) != 0;
  }
  float worldGravityZ() const {
    return GetField<float>(VT_WORLDGRAVITYZ, 0.0f);
  }
  /// Game speed multiplier, 1.0 is regular game speed.
  float gameSpeed() const {
    return GetField<float>(VT_GAMESPEED, 0.0f);
  }
  /// Tracks the number of physics frames the game has computed.
  /// May increase by more than one across consecutive packets.
  /// Data type will roll over after 207 days at 120Hz.
  int32_t frameNum() const {
    return GetField<int32_t>(VT_FRAMENUM, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<float>(verifier, VT_SECONDSELAPSED) &&
           VerifyField<float>(verifier, VT_GAMETIMEREMAINING) &&
           VerifyField<uint8_t>(verifier, VT_ISOVERTIME) &&
           VerifyField<uint8_t>(verifier, VT_ISUNLIMITEDTIME) &&
           VerifyField<uint8_t>(verifier, VT_ISROUNDACTIVE) &&
           VerifyField<uint8_t>(verifier, VT_ISKICKOFFPAUSE) &&
           VerifyField<uint8_t>(verifier, VT_ISMATCHENDED) &&
           VerifyField<float>(verifier, VT_WORLDGRAVITYZ) &&
           VerifyField<float>(verifier, VT_GAMESPEED) &&
           VerifyField<int32_t>(verifier, VT_FRAMENUM) &&
           verifier.EndTable();
  }
};

struct GameInfoBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_secondsElapsed(float secondsElapsed) {
    fbb_.AddElement<float>(GameInfo::VT_SECONDSELAPSED, secondsElapsed, 0.0f);
  }
  void add_gameTimeRemaining(float gameTimeRemaining) {
    fbb_.AddElement<float>(GameInfo::VT_GAMETIMEREMAINING, gameTimeRemaining, 0.0f);
  }
  void add_isOvertime(bool isOvertime) {
    fbb_.AddElement<uint8_t>(GameInfo::VT_ISOVERTIME, static_cast<uint8_t>(isOvertime), 0);
  }
  void add_isUnlimitedTime(bool isUnlimitedTime) {
    fbb_.AddElement<uint8_t>(GameInfo::VT_ISUNLIMITEDTIME, static_cast<uint8_t>(isUnlimitedTime), 0);
  }
  void add_isRoundActive(bool isRoundActive) {
    fbb_.AddElement<uint8_t>(GameInfo::VT_ISROUNDACTIVE, static_cast<uint8_t>(isRoundActive), 0);
  }
  void add_isKickoffPause(bool isKickoffPause) {
    fbb_.AddElement<uint8_t>(GameInfo::VT_ISKICKOFFPAUSE, static_cast<uint8_t>(isKickoffPause), 0);
  }
  void add_isMatchEnded(bool isMatchEnded) {
    fbb_.AddElement<uint8_t>(GameInfo::VT_ISMATCHENDED, static_cast<uint8_t>(isMatchEnded), 0);
  }
  void add_worldGravityZ(float worldGravityZ) {
    fbb_.AddElement<float>(GameInfo::VT_WORLDGRAVITYZ, worldGravityZ, 0.0f);
  }
  void add_gameSpeed(float gameSpeed) {
    fbb_.AddElement<float>(GameInfo::VT_GAMESPEED, gameSpeed, 0.0f);
  }
  void add_frameNum(int32_t frameNum) {
    fbb_.AddElement<int32_t>(GameInfo::VT_FRAMENUM, frameNum, 0);
  }
  explicit GameInfoBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  GameInfoBuilder &operator=(const GameInfoBuilder &);
  flatbuffers::Offset<GameInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<GameInfo>(end);
    return o;
  }
};

inline flatbuffers::Offset<GameInfo> CreateGameInfo(
    flatbuffers::FlatBufferBuilder &_fbb,
    float secondsElapsed = 0.0f,
    float gameTimeRemaining = 0.0f,
    bool isOvertime = false,
    bool isUnlimitedTime = false,
    bool isRoundActive = false,
    bool isKickoffPause = false,
    bool isMatchEnded = false,
    float worldGravityZ = 0.0f,
    float gameSpeed = 0.0f,
    int32_t frameNum = 0) {
  GameInfoBuilder builder_(_fbb);
  builder_.add_frameNum(frameNum);
  builder_.add_gameSpeed(gameSpeed);
  builder_.add_worldGravityZ(worldGravityZ);
  builder_.add_gameTimeRemaining(gameTimeRemaining);
  builder_.add_secondsElapsed(secondsElapsed);
  builder_.add_isMatchEnded(isMatchEnded);
  builder_.add_isKickoffPause(isKickoffPause);
  builder_.add_isRoundActive(isRoundActive);
  builder_.add_isUnlimitedTime(isUnlimitedTime);
  builder_.add_isOvertime(isOvertime);
  return builder_.Finish();
}

struct TeamInfo FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_TEAMINDEX = 4,
    VT_SCORE = 6
  };
  int32_t teamIndex() const {
    return GetField<int32_t>(VT_TEAMINDEX, 0);
  }
  /// number of goals scored.
  int32_t score() const {
    return GetField<int32_t>(VT_SCORE, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_TEAMINDEX) &&
           VerifyField<int32_t>(verifier, VT_SCORE) &&
           verifier.EndTable();
  }
};

struct TeamInfoBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_teamIndex(int32_t teamIndex) {
    fbb_.AddElement<int32_t>(TeamInfo::VT_TEAMINDEX, teamIndex, 0);
  }
  void add_score(int32_t score) {
    fbb_.AddElement<int32_t>(TeamInfo::VT_SCORE, score, 0);
  }
  explicit TeamInfoBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  TeamInfoBuilder &operator=(const TeamInfoBuilder &);
  flatbuffers::Offset<TeamInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<TeamInfo>(end);
    return o;
  }
};

inline flatbuffers::Offset<TeamInfo> CreateTeamInfo(
    flatbuffers::FlatBufferBuilder &_fbb,
    int32_t teamIndex = 0,
    int32_t score = 0) {
  TeamInfoBuilder builder_(_fbb);
  builder_.add_score(score);
  builder_.add_teamIndex(teamIndex);
  return builder_.Finish();
}

struct GameTickPacket FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_PLAYERS = 4,
    VT_BOOSTPADSTATES = 6,
    VT_BALL = 8,
    VT_GAMEINFO = 10,
    VT_TILEINFORMATION = 12,
    VT_TEAMS = 14
  };
  const flatbuffers::Vector<flatbuffers::Offset<PlayerInfo>> *players() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<PlayerInfo>> *>(VT_PLAYERS);
  }
  const flatbuffers::Vector<flatbuffers::Offset<BoostPadState>> *boostPadStates() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<BoostPadState>> *>(VT_BOOSTPADSTATES);
  }
  const BallInfo *ball() const {
    return GetPointer<const BallInfo *>(VT_BALL);
  }
  const GameInfo *gameInfo() const {
    return GetPointer<const GameInfo *>(VT_GAMEINFO);
  }
  const flatbuffers::Vector<flatbuffers::Offset<DropshotTile>> *tileInformation() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<DropshotTile>> *>(VT_TILEINFORMATION);
  }
  const flatbuffers::Vector<flatbuffers::Offset<TeamInfo>> *teams() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<TeamInfo>> *>(VT_TEAMS);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PLAYERS) &&
           verifier.Verify(players()) &&
           verifier.VerifyVectorOfTables(players()) &&
           VerifyOffset(verifier, VT_BOOSTPADSTATES) &&
           verifier.Verify(boostPadStates()) &&
           verifier.VerifyVectorOfTables(boostPadStates()) &&
           VerifyOffset(verifier, VT_BALL) &&
           verifier.VerifyTable(ball()) &&
           VerifyOffset(verifier, VT_GAMEINFO) &&
           verifier.VerifyTable(gameInfo()) &&
           VerifyOffset(verifier, VT_TILEINFORMATION) &&
           verifier.Verify(tileInformation()) &&
           verifier.VerifyVectorOfTables(tileInformation()) &&
           VerifyOffset(verifier, VT_TEAMS) &&
           verifier.Verify(teams()) &&
           verifier.VerifyVectorOfTables(teams()) &&
           verifier.EndTable();
  }
};

struct GameTickPacketBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_players(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<PlayerInfo>>> players) {
    fbb_.AddOffset(GameTickPacket::VT_PLAYERS, players);
  }
  void add_boostPadStates(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<BoostPadState>>> boostPadStates) {
    fbb_.AddOffset(GameTickPacket::VT_BOOSTPADSTATES, boostPadStates);
  }
  void add_ball(flatbuffers::Offset<BallInfo> ball) {
    fbb_.AddOffset(GameTickPacket::VT_BALL, ball);
  }
  void add_gameInfo(flatbuffers::Offset<GameInfo> gameInfo) {
    fbb_.AddOffset(GameTickPacket::VT_GAMEINFO, gameInfo);
  }
  void add_tileInformation(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<DropshotTile>>> tileInformation) {
    fbb_.AddOffset(GameTickPacket::VT_TILEINFORMATION, tileInformation);
  }
  void add_teams(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<TeamInfo>>> teams) {
    fbb_.AddOffset(GameTickPacket::VT_TEAMS, teams);
  }
  explicit GameTickPacketBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  GameTickPacketBuilder &operator=(const GameTickPacketBuilder &);
  flatbuffers::Offset<GameTickPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<GameTickPacket>(end);
    return o;
  }
};

inline flatbuffers::Offset<GameTickPacket> CreateGameTickPacket(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<PlayerInfo>>> players = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<BoostPadState>>> boostPadStates = 0,
    flatbuffers::Offset<BallInfo> ball = 0,
    flatbuffers::Offset<GameInfo> gameInfo = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<DropshotTile>>> tileInformation = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<TeamInfo>>> teams = 0) {
  GameTickPacketBuilder builder_(_fbb);
  builder_.add_teams(teams);
  builder_.add_tileInformation(tileInformation);
  builder_.add_gameInfo(gameInfo);
  builder_.add_ball(ball);
  builder_.add_boostPadStates(boostPadStates);
  builder_.add_players(players);
  return builder_.Finish();
}

inline flatbuffers::Offset<GameTickPacket> CreateGameTickPacketDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<flatbuffers::Offset<PlayerInfo>> *players = nullptr,
    const std::vector<flatbuffers::Offset<BoostPadState>> *boostPadStates = nullptr,
    flatbuffers::Offset<BallInfo> ball = 0,
    flatbuffers::Offset<GameInfo> gameInfo = 0,
    const std::vector<flatbuffers::Offset<DropshotTile>> *tileInformation = nullptr,
    const std::vector<flatbuffers::Offset<TeamInfo>> *teams = nullptr) {
  return rlbot::flat::CreateGameTickPacket(
      _fbb,
      players ? _fbb.CreateVector<flatbuffers::Offset<PlayerInfo>>(*players) : 0,
      boostPadStates ? _fbb.CreateVector<flatbuffers::Offset<BoostPadState>>(*boostPadStates) : 0,
      ball,
      gameInfo,
      tileInformation ? _fbb.CreateVector<flatbuffers::Offset<DropshotTile>>(*tileInformation) : 0,
      teams ? _fbb.CreateVector<flatbuffers::Offset<TeamInfo>>(*teams) : 0);
}

/// The state of a rigid body in Rocket League's physics engine.
/// This gets updated in time with the physics tick, not the rendering framerate.
/// The frame field will be incremented every time the physics engine ticks.
struct RigidBodyState FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_FRAME = 4,
    VT_LOCATION = 6,
    VT_ROTATION = 8,
    VT_VELOCITY = 10,
    VT_ANGULARVELOCITY = 12
  };
  int32_t frame() const {
    return GetField<int32_t>(VT_FRAME, 0);
  }
  const Vector3 *location() const {
    return GetStruct<const Vector3 *>(VT_LOCATION);
  }
  const Quaternion *rotation() const {
    return GetStruct<const Quaternion *>(VT_ROTATION);
  }
  const Vector3 *velocity() const {
    return GetStruct<const Vector3 *>(VT_VELOCITY);
  }
  const Vector3 *angularVelocity() const {
    return GetStruct<const Vector3 *>(VT_ANGULARVELOCITY);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_FRAME) &&
           VerifyField<Vector3>(verifier, VT_LOCATION) &&
           VerifyField<Quaternion>(verifier, VT_ROTATION) &&
           VerifyField<Vector3>(verifier, VT_VELOCITY) &&
           VerifyField<Vector3>(verifier, VT_ANGULARVELOCITY) &&
           verifier.EndTable();
  }
};

struct RigidBodyStateBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_frame(int32_t frame) {
    fbb_.AddElement<int32_t>(RigidBodyState::VT_FRAME, frame, 0);
  }
  void add_location(const Vector3 *location) {
    fbb_.AddStruct(RigidBodyState::VT_LOCATION, location);
  }
  void add_rotation(const Quaternion *rotation) {
    fbb_.AddStruct(RigidBodyState::VT_ROTATION, rotation);
  }
  void add_velocity(const Vector3 *velocity) {
    fbb_.AddStruct(RigidBodyState::VT_VELOCITY, velocity);
  }
  void add_angularVelocity(const Vector3 *angularVelocity) {
    fbb_.AddStruct(RigidBodyState::VT_ANGULARVELOCITY, angularVelocity);
  }
  explicit RigidBodyStateBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  RigidBodyStateBuilder &operator=(const RigidBodyStateBuilder &);
  flatbuffers::Offset<RigidBodyState> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<RigidBodyState>(end);
    return o;
  }
};

inline flatbuffers::Offset<RigidBodyState> CreateRigidBodyState(
    flatbuffers::FlatBufferBuilder &_fbb,
    int32_t frame = 0,
    const Vector3 *location = 0,
    const Quaternion *rotation = 0,
    const Vector3 *velocity = 0,
    const Vector3 *angularVelocity = 0) {
  RigidBodyStateBuilder builder_(_fbb);
  builder_.add_angularVelocity(angularVelocity);
  builder_.add_velocity(velocity);
  builder_.add_rotation(rotation);
  builder_.add_location(location);
  builder_.add_frame(frame);
  return builder_.Finish();
}

/// Rigid body state for a player / car in the game. Includes the latest
/// controller input, which is otherwise difficult to correlate with consequences.
struct PlayerRigidBodyState FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_STATE = 4,
    VT_INPUT = 6
  };
  const RigidBodyState *state() const {
    return GetPointer<const RigidBodyState *>(VT_STATE);
  }
  const ControllerState *input() const {
    return GetPointer<const ControllerState *>(VT_INPUT);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_STATE) &&
           verifier.VerifyTable(state()) &&
           VerifyOffset(verifier, VT_INPUT) &&
           verifier.VerifyTable(input()) &&
           verifier.EndTable();
  }
};

struct PlayerRigidBodyStateBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_state(flatbuffers::Offset<RigidBodyState> state) {
    fbb_.AddOffset(PlayerRigidBodyState::VT_STATE, state);
  }
  void add_input(flatbuffers::Offset<ControllerState> input) {
    fbb_.AddOffset(PlayerRigidBodyState::VT_INPUT, input);
  }
  explicit PlayerRigidBodyStateBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  PlayerRigidBodyStateBuilder &operator=(const PlayerRigidBodyStateBuilder &);
  flatbuffers::Offset<PlayerRigidBodyState> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<PlayerRigidBodyState>(end);
    return o;
  }
};

inline flatbuffers::Offset<PlayerRigidBodyState> CreatePlayerRigidBodyState(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<RigidBodyState> state = 0,
    flatbuffers::Offset<ControllerState> input = 0) {
  PlayerRigidBodyStateBuilder builder_(_fbb);
  builder_.add_input(input);
  builder_.add_state(state);
  return builder_.Finish();
}

/// Rigid body state for the ball.
struct BallRigidBodyState FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_STATE = 4
  };
  const RigidBodyState *state() const {
    return GetPointer<const RigidBodyState *>(VT_STATE);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_STATE) &&
           verifier.VerifyTable(state()) &&
           verifier.EndTable();
  }
};

struct BallRigidBodyStateBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_state(flatbuffers::Offset<RigidBodyState> state) {
    fbb_.AddOffset(BallRigidBodyState::VT_STATE, state);
  }
  explicit BallRigidBodyStateBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  BallRigidBodyStateBuilder &operator=(const BallRigidBodyStateBuilder &);
  flatbuffers::Offset<BallRigidBodyState> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<BallRigidBodyState>(end);
    return o;
  }
};

inline flatbuffers::Offset<BallRigidBodyState> CreateBallRigidBodyState(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<RigidBodyState> state = 0) {
  BallRigidBodyStateBuilder builder_(_fbb);
  builder_.add_state(state);
  return builder_.Finish();
}

/// Contains all rigid body state information.
struct RigidBodyTick FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_BALL = 4,
    VT_PLAYERS = 6
  };
  const BallRigidBodyState *ball() const {
    return GetPointer<const BallRigidBodyState *>(VT_BALL);
  }
  const flatbuffers::Vector<flatbuffers::Offset<PlayerRigidBodyState>> *players() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<PlayerRigidBodyState>> *>(VT_PLAYERS);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_BALL) &&
           verifier.VerifyTable(ball()) &&
           VerifyOffset(verifier, VT_PLAYERS) &&
           verifier.Verify(players()) &&
           verifier.VerifyVectorOfTables(players()) &&
           verifier.EndTable();
  }
};

struct RigidBodyTickBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_ball(flatbuffers::Offset<BallRigidBodyState> ball) {
    fbb_.AddOffset(RigidBodyTick::VT_BALL, ball);
  }
  void add_players(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<PlayerRigidBodyState>>> players) {
    fbb_.AddOffset(RigidBodyTick::VT_PLAYERS, players);
  }
  explicit RigidBodyTickBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  RigidBodyTickBuilder &operator=(const RigidBodyTickBuilder &);
  flatbuffers::Offset<RigidBodyTick> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<RigidBodyTick>(end);
    return o;
  }
};

inline flatbuffers::Offset<RigidBodyTick> CreateRigidBodyTick(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<BallRigidBodyState> ball = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<PlayerRigidBodyState>>> players = 0) {
  RigidBodyTickBuilder builder_(_fbb);
  builder_.add_players(players);
  builder_.add_ball(ball);
  return builder_.Finish();
}

inline flatbuffers::Offset<RigidBodyTick> CreateRigidBodyTickDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<BallRigidBodyState> ball = 0,
    const std::vector<flatbuffers::Offset<PlayerRigidBodyState>> *players = nullptr) {
  return rlbot::flat::CreateRigidBodyTick(
      _fbb,
      ball,
      players ? _fbb.CreateVector<flatbuffers::Offset<PlayerRigidBodyState>>(*players) : 0);
}

struct GoalInfo FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_TEAMNUM = 4,
    VT_LOCATION = 6,
    VT_DIRECTION = 8,
    VT_WIDTH = 10,
    VT_HEIGHT = 12
  };
  int32_t teamNum() const {
    return GetField<int32_t>(VT_TEAMNUM, 0);
  }
  const Vector3 *location() const {
    return GetStruct<const Vector3 *>(VT_LOCATION);
  }
  const Vector3 *direction() const {
    return GetStruct<const Vector3 *>(VT_DIRECTION);
  }
  float width() const {
    return GetField<float>(VT_WIDTH, 0.0f);
  }
  float height() const {
    return GetField<float>(VT_HEIGHT, 0.0f);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_TEAMNUM) &&
           VerifyField<Vector3>(verifier, VT_LOCATION) &&
           VerifyField<Vector3>(verifier, VT_DIRECTION) &&
           VerifyField<float>(verifier, VT_WIDTH) &&
           VerifyField<float>(verifier, VT_HEIGHT) &&
           verifier.EndTable();
  }
};

struct GoalInfoBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_teamNum(int32_t teamNum) {
    fbb_.AddElement<int32_t>(GoalInfo::VT_TEAMNUM, teamNum, 0);
  }
  void add_location(const Vector3 *location) {
    fbb_.AddStruct(GoalInfo::VT_LOCATION, location);
  }
  void add_direction(const Vector3 *direction) {
    fbb_.AddStruct(GoalInfo::VT_DIRECTION, direction);
  }
  void add_width(float width) {
    fbb_.AddElement<float>(GoalInfo::VT_WIDTH, width, 0.0f);
  }
  void add_height(float height) {
    fbb_.AddElement<float>(GoalInfo::VT_HEIGHT, height, 0.0f);
  }
  explicit GoalInfoBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  GoalInfoBuilder &operator=(const GoalInfoBuilder &);
  flatbuffers::Offset<GoalInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<GoalInfo>(end);
    return o;
  }
};

inline flatbuffers::Offset<GoalInfo> CreateGoalInfo(
    flatbuffers::FlatBufferBuilder &_fbb,
    int32_t teamNum = 0,
    const Vector3 *location = 0,
    const Vector3 *direction = 0,
    float width = 0.0f,
    float height = 0.0f) {
  GoalInfoBuilder builder_(_fbb);
  builder_.add_height(height);
  builder_.add_width(width);
  builder_.add_direction(direction);
  builder_.add_location(location);
  builder_.add_teamNum(teamNum);
  return builder_.Finish();
}

struct BoostPad FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_LOCATION = 4,
    VT_ISFULLBOOST = 6
  };
  const Vector3 *location() const {
    return GetStruct<const Vector3 *>(VT_LOCATION);
  }
  bool isFullBoost() const {
    return GetField<uint8_t>(VT_ISFULLBOOST, 0) != 0;
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<Vector3>(verifier, VT_LOCATION) &&
           VerifyField<uint8_t>(verifier, VT_ISFULLBOOST) &&
           verifier.EndTable();
  }
};

struct BoostPadBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_location(const Vector3 *location) {
    fbb_.AddStruct(BoostPad::VT_LOCATION, location);
  }
  void add_isFullBoost(bool isFullBoost) {
    fbb_.AddElement<uint8_t>(BoostPad::VT_ISFULLBOOST, static_cast<uint8_t>(isFullBoost), 0);
  }
  explicit BoostPadBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  BoostPadBuilder &operator=(const BoostPadBuilder &);
  flatbuffers::Offset<BoostPad> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<BoostPad>(end);
    return o;
  }
};

inline flatbuffers::Offset<BoostPad> CreateBoostPad(
    flatbuffers::FlatBufferBuilder &_fbb,
    const Vector3 *location = 0,
    bool isFullBoost = false) {
  BoostPadBuilder builder_(_fbb);
  builder_.add_location(location);
  builder_.add_isFullBoost(isFullBoost);
  return builder_.Finish();
}

struct FieldInfo FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_BOOSTPADS = 4,
    VT_GOALS = 6
  };
  const flatbuffers::Vector<flatbuffers::Offset<BoostPad>> *boostPads() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<BoostPad>> *>(VT_BOOSTPADS);
  }
  const flatbuffers::Vector<flatbuffers::Offset<GoalInfo>> *goals() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<GoalInfo>> *>(VT_GOALS);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_BOOSTPADS) &&
           verifier.Verify(boostPads()) &&
           verifier.VerifyVectorOfTables(boostPads()) &&
           VerifyOffset(verifier, VT_GOALS) &&
           verifier.Verify(goals()) &&
           verifier.VerifyVectorOfTables(goals()) &&
           verifier.EndTable();
  }
};

struct FieldInfoBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_boostPads(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<BoostPad>>> boostPads) {
    fbb_.AddOffset(FieldInfo::VT_BOOSTPADS, boostPads);
  }
  void add_goals(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<GoalInfo>>> goals) {
    fbb_.AddOffset(FieldInfo::VT_GOALS, goals);
  }
  explicit FieldInfoBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  FieldInfoBuilder &operator=(const FieldInfoBuilder &);
  flatbuffers::Offset<FieldInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<FieldInfo>(end);
    return o;
  }
};

inline flatbuffers::Offset<FieldInfo> CreateFieldInfo(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<BoostPad>>> boostPads = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<GoalInfo>>> goals = 0) {
  FieldInfoBuilder builder_(_fbb);
  builder_.add_goals(goals);
  builder_.add_boostPads(boostPads);
  return builder_.Finish();
}

inline flatbuffers::Offset<FieldInfo> CreateFieldInfoDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<flatbuffers::Offset<BoostPad>> *boostPads = nullptr,
    const std::vector<flatbuffers::Offset<GoalInfo>> *goals = nullptr) {
  return rlbot::flat::CreateFieldInfo(
      _fbb,
      boostPads ? _fbb.CreateVector<flatbuffers::Offset<BoostPad>>(*boostPads) : 0,
      goals ? _fbb.CreateVector<flatbuffers::Offset<GoalInfo>>(*goals) : 0);
}

struct Vector3Partial FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_X = 4,
    VT_Y = 6,
    VT_Z = 8
  };
  const Float *x() const {
    return GetStruct<const Float *>(VT_X);
  }
  const Float *y() const {
    return GetStruct<const Float *>(VT_Y);
  }
  const Float *z() const {
    return GetStruct<const Float *>(VT_Z);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<Float>(verifier, VT_X) &&
           VerifyField<Float>(verifier, VT_Y) &&
           VerifyField<Float>(verifier, VT_Z) &&
           verifier.EndTable();
  }
};

struct Vector3PartialBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_x(const Float *x) {
    fbb_.AddStruct(Vector3Partial::VT_X, x);
  }
  void add_y(const Float *y) {
    fbb_.AddStruct(Vector3Partial::VT_Y, y);
  }
  void add_z(const Float *z) {
    fbb_.AddStruct(Vector3Partial::VT_Z, z);
  }
  explicit Vector3PartialBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  Vector3PartialBuilder &operator=(const Vector3PartialBuilder &);
  flatbuffers::Offset<Vector3Partial> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<Vector3Partial>(end);
    return o;
  }
};

inline flatbuffers::Offset<Vector3Partial> CreateVector3Partial(
    flatbuffers::FlatBufferBuilder &_fbb,
    const Float *x = 0,
    const Float *y = 0,
    const Float *z = 0) {
  Vector3PartialBuilder builder_(_fbb);
  builder_.add_z(z);
  builder_.add_y(y);
  builder_.add_x(x);
  return builder_.Finish();
}

struct RotatorPartial FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_PITCH = 4,
    VT_YAW = 6,
    VT_ROLL = 8
  };
  const Float *pitch() const {
    return GetStruct<const Float *>(VT_PITCH);
  }
  const Float *yaw() const {
    return GetStruct<const Float *>(VT_YAW);
  }
  const Float *roll() const {
    return GetStruct<const Float *>(VT_ROLL);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<Float>(verifier, VT_PITCH) &&
           VerifyField<Float>(verifier, VT_YAW) &&
           VerifyField<Float>(verifier, VT_ROLL) &&
           verifier.EndTable();
  }
};

struct RotatorPartialBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_pitch(const Float *pitch) {
    fbb_.AddStruct(RotatorPartial::VT_PITCH, pitch);
  }
  void add_yaw(const Float *yaw) {
    fbb_.AddStruct(RotatorPartial::VT_YAW, yaw);
  }
  void add_roll(const Float *roll) {
    fbb_.AddStruct(RotatorPartial::VT_ROLL, roll);
  }
  explicit RotatorPartialBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  RotatorPartialBuilder &operator=(const RotatorPartialBuilder &);
  flatbuffers::Offset<RotatorPartial> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<RotatorPartial>(end);
    return o;
  }
};

inline flatbuffers::Offset<RotatorPartial> CreateRotatorPartial(
    flatbuffers::FlatBufferBuilder &_fbb,
    const Float *pitch = 0,
    const Float *yaw = 0,
    const Float *roll = 0) {
  RotatorPartialBuilder builder_(_fbb);
  builder_.add_roll(roll);
  builder_.add_yaw(yaw);
  builder_.add_pitch(pitch);
  return builder_.Finish();
}

struct DesiredPhysics FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_LOCATION = 4,
    VT_ROTATION = 6,
    VT_VELOCITY = 8,
    VT_ANGULARVELOCITY = 10
  };
  const Vector3Partial *location() const {
    return GetPointer<const Vector3Partial *>(VT_LOCATION);
  }
  const RotatorPartial *rotation() const {
    return GetPointer<const RotatorPartial *>(VT_ROTATION);
  }
  const Vector3Partial *velocity() const {
    return GetPointer<const Vector3Partial *>(VT_VELOCITY);
  }
  const Vector3Partial *angularVelocity() const {
    return GetPointer<const Vector3Partial *>(VT_ANGULARVELOCITY);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_LOCATION) &&
           verifier.VerifyTable(location()) &&
           VerifyOffset(verifier, VT_ROTATION) &&
           verifier.VerifyTable(rotation()) &&
           VerifyOffset(verifier, VT_VELOCITY) &&
           verifier.VerifyTable(velocity()) &&
           VerifyOffset(verifier, VT_ANGULARVELOCITY) &&
           verifier.VerifyTable(angularVelocity()) &&
           verifier.EndTable();
  }
};

struct DesiredPhysicsBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_location(flatbuffers::Offset<Vector3Partial> location) {
    fbb_.AddOffset(DesiredPhysics::VT_LOCATION, location);
  }
  void add_rotation(flatbuffers::Offset<RotatorPartial> rotation) {
    fbb_.AddOffset(DesiredPhysics::VT_ROTATION, rotation);
  }
  void add_velocity(flatbuffers::Offset<Vector3Partial> velocity) {
    fbb_.AddOffset(DesiredPhysics::VT_VELOCITY, velocity);
  }
  void add_angularVelocity(flatbuffers::Offset<Vector3Partial> angularVelocity) {
    fbb_.AddOffset(DesiredPhysics::VT_ANGULARVELOCITY, angularVelocity);
  }
  explicit DesiredPhysicsBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  DesiredPhysicsBuilder &operator=(const DesiredPhysicsBuilder &);
  flatbuffers::Offset<DesiredPhysics> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<DesiredPhysics>(end);
    return o;
  }
};

inline flatbuffers::Offset<DesiredPhysics> CreateDesiredPhysics(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<Vector3Partial> location = 0,
    flatbuffers::Offset<RotatorPartial> rotation = 0,
    flatbuffers::Offset<Vector3Partial> velocity = 0,
    flatbuffers::Offset<Vector3Partial> angularVelocity = 0) {
  DesiredPhysicsBuilder builder_(_fbb);
  builder_.add_angularVelocity(angularVelocity);
  builder_.add_velocity(velocity);
  builder_.add_rotation(rotation);
  builder_.add_location(location);
  return builder_.Finish();
}

struct DesiredBallState FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_PHYSICS = 4
  };
  const DesiredPhysics *physics() const {
    return GetPointer<const DesiredPhysics *>(VT_PHYSICS);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PHYSICS) &&
           verifier.VerifyTable(physics()) &&
           verifier.EndTable();
  }
};

struct DesiredBallStateBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_physics(flatbuffers::Offset<DesiredPhysics> physics) {
    fbb_.AddOffset(DesiredBallState::VT_PHYSICS, physics);
  }
  explicit DesiredBallStateBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  DesiredBallStateBuilder &operator=(const DesiredBallStateBuilder &);
  flatbuffers::Offset<DesiredBallState> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<DesiredBallState>(end);
    return o;
  }
};

inline flatbuffers::Offset<DesiredBallState> CreateDesiredBallState(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<DesiredPhysics> physics = 0) {
  DesiredBallStateBuilder builder_(_fbb);
  builder_.add_physics(physics);
  return builder_.Finish();
}

struct DesiredCarState FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_PHYSICS = 4,
    VT_BOOSTAMOUNT = 6,
    VT_JUMPED = 8,
    VT_DOUBLEJUMPED = 10
  };
  const DesiredPhysics *physics() const {
    return GetPointer<const DesiredPhysics *>(VT_PHYSICS);
  }
  const Float *boostAmount() const {
    return GetStruct<const Float *>(VT_BOOSTAMOUNT);
  }
  const Bool *jumped() const {
    return GetStruct<const Bool *>(VT_JUMPED);
  }
  const Bool *doubleJumped() const {
    return GetStruct<const Bool *>(VT_DOUBLEJUMPED);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PHYSICS) &&
           verifier.VerifyTable(physics()) &&
           VerifyField<Float>(verifier, VT_BOOSTAMOUNT) &&
           VerifyField<Bool>(verifier, VT_JUMPED) &&
           VerifyField<Bool>(verifier, VT_DOUBLEJUMPED) &&
           verifier.EndTable();
  }
};

struct DesiredCarStateBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_physics(flatbuffers::Offset<DesiredPhysics> physics) {
    fbb_.AddOffset(DesiredCarState::VT_PHYSICS, physics);
  }
  void add_boostAmount(const Float *boostAmount) {
    fbb_.AddStruct(DesiredCarState::VT_BOOSTAMOUNT, boostAmount);
  }
  void add_jumped(const Bool *jumped) {
    fbb_.AddStruct(DesiredCarState::VT_JUMPED, jumped);
  }
  void add_doubleJumped(const Bool *doubleJumped) {
    fbb_.AddStruct(DesiredCarState::VT_DOUBLEJUMPED, doubleJumped);
  }
  explicit DesiredCarStateBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  DesiredCarStateBuilder &operator=(const DesiredCarStateBuilder &);
  flatbuffers::Offset<DesiredCarState> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<DesiredCarState>(end);
    return o;
  }
};

inline flatbuffers::Offset<DesiredCarState> CreateDesiredCarState(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<DesiredPhysics> physics = 0,
    const Float *boostAmount = 0,
    const Bool *jumped = 0,
    const Bool *doubleJumped = 0) {
  DesiredCarStateBuilder builder_(_fbb);
  builder_.add_doubleJumped(doubleJumped);
  builder_.add_jumped(jumped);
  builder_.add_boostAmount(boostAmount);
  builder_.add_physics(physics);
  return builder_.Finish();
}

struct DesiredBoostState FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_RESPAWNTIME = 4
  };
  const Float *respawnTime() const {
    return GetStruct<const Float *>(VT_RESPAWNTIME);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<Float>(verifier, VT_RESPAWNTIME) &&
           verifier.EndTable();
  }
};

struct DesiredBoostStateBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_respawnTime(const Float *respawnTime) {
    fbb_.AddStruct(DesiredBoostState::VT_RESPAWNTIME, respawnTime);
  }
  explicit DesiredBoostStateBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  DesiredBoostStateBuilder &operator=(const DesiredBoostStateBuilder &);
  flatbuffers::Offset<DesiredBoostState> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<DesiredBoostState>(end);
    return o;
  }
};

inline flatbuffers::Offset<DesiredBoostState> CreateDesiredBoostState(
    flatbuffers::FlatBufferBuilder &_fbb,
    const Float *respawnTime = 0) {
  DesiredBoostStateBuilder builder_(_fbb);
  builder_.add_respawnTime(respawnTime);
  return builder_.Finish();
}

struct DesiredGameInfoState FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_WORLDGRAVITYZ = 4,
    VT_GAMESPEED = 6,
    VT_PAUSED = 8,
    VT_ENDMATCH = 10
  };
  const Float *worldGravityZ() const {
    return GetStruct<const Float *>(VT_WORLDGRAVITYZ);
  }
  const Float *gameSpeed() const {
    return GetStruct<const Float *>(VT_GAMESPEED);
  }
  const Bool *paused() const {
    return GetStruct<const Bool *>(VT_PAUSED);
  }
  const Bool *endMatch() const {
    return GetStruct<const Bool *>(VT_ENDMATCH);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<Float>(verifier, VT_WORLDGRAVITYZ) &&
           VerifyField<Float>(verifier, VT_GAMESPEED) &&
           VerifyField<Bool>(verifier, VT_PAUSED) &&
           VerifyField<Bool>(verifier, VT_ENDMATCH) &&
           verifier.EndTable();
  }
};

struct DesiredGameInfoStateBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_worldGravityZ(const Float *worldGravityZ) {
    fbb_.AddStruct(DesiredGameInfoState::VT_WORLDGRAVITYZ, worldGravityZ);
  }
  void add_gameSpeed(const Float *gameSpeed) {
    fbb_.AddStruct(DesiredGameInfoState::VT_GAMESPEED, gameSpeed);
  }
  void add_paused(const Bool *paused) {
    fbb_.AddStruct(DesiredGameInfoState::VT_PAUSED, paused);
  }
  void add_endMatch(const Bool *endMatch) {
    fbb_.AddStruct(DesiredGameInfoState::VT_ENDMATCH, endMatch);
  }
  explicit DesiredGameInfoStateBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  DesiredGameInfoStateBuilder &operator=(const DesiredGameInfoStateBuilder &);
  flatbuffers::Offset<DesiredGameInfoState> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<DesiredGameInfoState>(end);
    return o;
  }
};

inline flatbuffers::Offset<DesiredGameInfoState> CreateDesiredGameInfoState(
    flatbuffers::FlatBufferBuilder &_fbb,
    const Float *worldGravityZ = 0,
    const Float *gameSpeed = 0,
    const Bool *paused = 0,
    const Bool *endMatch = 0) {
  DesiredGameInfoStateBuilder builder_(_fbb);
  builder_.add_endMatch(endMatch);
  builder_.add_paused(paused);
  builder_.add_gameSpeed(gameSpeed);
  builder_.add_worldGravityZ(worldGravityZ);
  return builder_.Finish();
}

/// A console command which we will try to execute inside Rocket League.
/// See https://github.com/RLBot/RLBot/wiki/Console-Commands for a list of known commands.
struct ConsoleCommand FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_COMMAND = 4
  };
  const flatbuffers::String *command() const {
    return GetPointer<const flatbuffers::String *>(VT_COMMAND);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_COMMAND) &&
           verifier.Verify(command()) &&
           verifier.EndTable();
  }
};

struct ConsoleCommandBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_command(flatbuffers::Offset<flatbuffers::String> command) {
    fbb_.AddOffset(ConsoleCommand::VT_COMMAND, command);
  }
  explicit ConsoleCommandBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ConsoleCommandBuilder &operator=(const ConsoleCommandBuilder &);
  flatbuffers::Offset<ConsoleCommand> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<ConsoleCommand>(end);
    return o;
  }
};

inline flatbuffers::Offset<ConsoleCommand> CreateConsoleCommand(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> command = 0) {
  ConsoleCommandBuilder builder_(_fbb);
  builder_.add_command(command);
  return builder_.Finish();
}

inline flatbuffers::Offset<ConsoleCommand> CreateConsoleCommandDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *command = nullptr) {
  return rlbot::flat::CreateConsoleCommand(
      _fbb,
      command ? _fbb.CreateString(command) : 0);
}

struct DesiredGameState FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_BALLSTATE = 4,
    VT_CARSTATES = 6,
    VT_BOOSTSTATES = 8,
    VT_GAMEINFOSTATE = 10,
    VT_CONSOLECOMMANDS = 12
  };
  const DesiredBallState *ballState() const {
    return GetPointer<const DesiredBallState *>(VT_BALLSTATE);
  }
  const flatbuffers::Vector<flatbuffers::Offset<DesiredCarState>> *carStates() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<DesiredCarState>> *>(VT_CARSTATES);
  }
  const flatbuffers::Vector<flatbuffers::Offset<DesiredBoostState>> *boostStates() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<DesiredBoostState>> *>(VT_BOOSTSTATES);
  }
  const DesiredGameInfoState *gameInfoState() const {
    return GetPointer<const DesiredGameInfoState *>(VT_GAMEINFOSTATE);
  }
  const flatbuffers::Vector<flatbuffers::Offset<ConsoleCommand>> *consoleCommands() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<ConsoleCommand>> *>(VT_CONSOLECOMMANDS);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_BALLSTATE) &&
           verifier.VerifyTable(ballState()) &&
           VerifyOffset(verifier, VT_CARSTATES) &&
           verifier.Verify(carStates()) &&
           verifier.VerifyVectorOfTables(carStates()) &&
           VerifyOffset(verifier, VT_BOOSTSTATES) &&
           verifier.Verify(boostStates()) &&
           verifier.VerifyVectorOfTables(boostStates()) &&
           VerifyOffset(verifier, VT_GAMEINFOSTATE) &&
           verifier.VerifyTable(gameInfoState()) &&
           VerifyOffset(verifier, VT_CONSOLECOMMANDS) &&
           verifier.Verify(consoleCommands()) &&
           verifier.VerifyVectorOfTables(consoleCommands()) &&
           verifier.EndTable();
  }
};

struct DesiredGameStateBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_ballState(flatbuffers::Offset<DesiredBallState> ballState) {
    fbb_.AddOffset(DesiredGameState::VT_BALLSTATE, ballState);
  }
  void add_carStates(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<DesiredCarState>>> carStates) {
    fbb_.AddOffset(DesiredGameState::VT_CARSTATES, carStates);
  }
  void add_boostStates(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<DesiredBoostState>>> boostStates) {
    fbb_.AddOffset(DesiredGameState::VT_BOOSTSTATES, boostStates);
  }
  void add_gameInfoState(flatbuffers::Offset<DesiredGameInfoState> gameInfoState) {
    fbb_.AddOffset(DesiredGameState::VT_GAMEINFOSTATE, gameInfoState);
  }
  void add_consoleCommands(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<ConsoleCommand>>> consoleCommands) {
    fbb_.AddOffset(DesiredGameState::VT_CONSOLECOMMANDS, consoleCommands);
  }
  explicit DesiredGameStateBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  DesiredGameStateBuilder &operator=(const DesiredGameStateBuilder &);
  flatbuffers::Offset<DesiredGameState> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<DesiredGameState>(end);
    return o;
  }
};

inline flatbuffers::Offset<DesiredGameState> CreateDesiredGameState(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<DesiredBallState> ballState = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<DesiredCarState>>> carStates = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<DesiredBoostState>>> boostStates = 0,
    flatbuffers::Offset<DesiredGameInfoState> gameInfoState = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<ConsoleCommand>>> consoleCommands = 0) {
  DesiredGameStateBuilder builder_(_fbb);
  builder_.add_consoleCommands(consoleCommands);
  builder_.add_gameInfoState(gameInfoState);
  builder_.add_boostStates(boostStates);
  builder_.add_carStates(carStates);
  builder_.add_ballState(ballState);
  return builder_.Finish();
}

inline flatbuffers::Offset<DesiredGameState> CreateDesiredGameStateDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<DesiredBallState> ballState = 0,
    const std::vector<flatbuffers::Offset<DesiredCarState>> *carStates = nullptr,
    const std::vector<flatbuffers::Offset<DesiredBoostState>> *boostStates = nullptr,
    flatbuffers::Offset<DesiredGameInfoState> gameInfoState = 0,
    const std::vector<flatbuffers::Offset<ConsoleCommand>> *consoleCommands = nullptr) {
  return rlbot::flat::CreateDesiredGameState(
      _fbb,
      ballState,
      carStates ? _fbb.CreateVector<flatbuffers::Offset<DesiredCarState>>(*carStates) : 0,
      boostStates ? _fbb.CreateVector<flatbuffers::Offset<DesiredBoostState>>(*boostStates) : 0,
      gameInfoState,
      consoleCommands ? _fbb.CreateVector<flatbuffers::Offset<ConsoleCommand>>(*consoleCommands) : 0);
}

struct Color FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_A = 4,
    VT_R = 6,
    VT_G = 8,
    VT_B = 10
  };
  uint8_t a() const {
    return GetField<uint8_t>(VT_A, 0);
  }
  uint8_t r() const {
    return GetField<uint8_t>(VT_R, 0);
  }
  uint8_t g() const {
    return GetField<uint8_t>(VT_G, 0);
  }
  uint8_t b() const {
    return GetField<uint8_t>(VT_B, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_A) &&
           VerifyField<uint8_t>(verifier, VT_R) &&
           VerifyField<uint8_t>(verifier, VT_G) &&
           VerifyField<uint8_t>(verifier, VT_B) &&
           verifier.EndTable();
  }
};

struct ColorBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_a(uint8_t a) {
    fbb_.AddElement<uint8_t>(Color::VT_A, a, 0);
  }
  void add_r(uint8_t r) {
    fbb_.AddElement<uint8_t>(Color::VT_R, r, 0);
  }
  void add_g(uint8_t g) {
    fbb_.AddElement<uint8_t>(Color::VT_G, g, 0);
  }
  void add_b(uint8_t b) {
    fbb_.AddElement<uint8_t>(Color::VT_B, b, 0);
  }
  explicit ColorBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ColorBuilder &operator=(const ColorBuilder &);
  flatbuffers::Offset<Color> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<Color>(end);
    return o;
  }
};

inline flatbuffers::Offset<Color> CreateColor(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t a = 0,
    uint8_t r = 0,
    uint8_t g = 0,
    uint8_t b = 0) {
  ColorBuilder builder_(_fbb);
  builder_.add_b(b);
  builder_.add_g(g);
  builder_.add_r(r);
  builder_.add_a(a);
  return builder_.Finish();
}

struct RenderMessage FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_RENDERTYPE = 4,
    VT_COLOR = 6,
    VT_START = 8,
    VT_END = 10,
    VT_SCALEX = 12,
    VT_SCALEY = 14,
    VT_TEXT = 16,
    VT_ISFILLED = 18
  };
  RenderType renderType() const {
    return static_cast<RenderType>(GetField<int8_t>(VT_RENDERTYPE, 1));
  }
  const Color *color() const {
    return GetPointer<const Color *>(VT_COLOR);
  }
  /// For 2d renders this only grabs x and y
  const Vector3 *start() const {
    return GetStruct<const Vector3 *>(VT_START);
  }
  /// For 2d renders this only grabs x and y
  const Vector3 *end() const {
    return GetStruct<const Vector3 *>(VT_END);
  }
  /// Scales the x size of the text/rectangle, is used for rectangles assuming an initial value of 1
  int32_t scaleX() const {
    return GetField<int32_t>(VT_SCALEX, 1);
  }
  /// Scales the y size of the text/rectangle, is used for rectangles assuming an initial value of 1
  int32_t scaleY() const {
    return GetField<int32_t>(VT_SCALEY, 1);
  }
  const flatbuffers::String *text() const {
    return GetPointer<const flatbuffers::String *>(VT_TEXT);
  }
  /// Rectangles can be filled or just outlines.
  bool isFilled() const {
    return GetField<uint8_t>(VT_ISFILLED, 0) != 0;
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_RENDERTYPE) &&
           VerifyOffset(verifier, VT_COLOR) &&
           verifier.VerifyTable(color()) &&
           VerifyField<Vector3>(verifier, VT_START) &&
           VerifyField<Vector3>(verifier, VT_END) &&
           VerifyField<int32_t>(verifier, VT_SCALEX) &&
           VerifyField<int32_t>(verifier, VT_SCALEY) &&
           VerifyOffset(verifier, VT_TEXT) &&
           verifier.Verify(text()) &&
           VerifyField<uint8_t>(verifier, VT_ISFILLED) &&
           verifier.EndTable();
  }
};

struct RenderMessageBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_renderType(RenderType renderType) {
    fbb_.AddElement<int8_t>(RenderMessage::VT_RENDERTYPE, static_cast<int8_t>(renderType), 1);
  }
  void add_color(flatbuffers::Offset<Color> color) {
    fbb_.AddOffset(RenderMessage::VT_COLOR, color);
  }
  void add_start(const Vector3 *start) {
    fbb_.AddStruct(RenderMessage::VT_START, start);
  }
  void add_end(const Vector3 *end) {
    fbb_.AddStruct(RenderMessage::VT_END, end);
  }
  void add_scaleX(int32_t scaleX) {
    fbb_.AddElement<int32_t>(RenderMessage::VT_SCALEX, scaleX, 1);
  }
  void add_scaleY(int32_t scaleY) {
    fbb_.AddElement<int32_t>(RenderMessage::VT_SCALEY, scaleY, 1);
  }
  void add_text(flatbuffers::Offset<flatbuffers::String> text) {
    fbb_.AddOffset(RenderMessage::VT_TEXT, text);
  }
  void add_isFilled(bool isFilled) {
    fbb_.AddElement<uint8_t>(RenderMessage::VT_ISFILLED, static_cast<uint8_t>(isFilled), 0);
  }
  explicit RenderMessageBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  RenderMessageBuilder &operator=(const RenderMessageBuilder &);
  flatbuffers::Offset<RenderMessage> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<RenderMessage>(end);
    return o;
  }
};

inline flatbuffers::Offset<RenderMessage> CreateRenderMessage(
    flatbuffers::FlatBufferBuilder &_fbb,
    RenderType renderType = RenderType_DrawLine2D,
    flatbuffers::Offset<Color> color = 0,
    const Vector3 *start = 0,
    const Vector3 *end = 0,
    int32_t scaleX = 1,
    int32_t scaleY = 1,
    flatbuffers::Offset<flatbuffers::String> text = 0,
    bool isFilled = false) {
  RenderMessageBuilder builder_(_fbb);
  builder_.add_text(text);
  builder_.add_scaleY(scaleY);
  builder_.add_scaleX(scaleX);
  builder_.add_end(end);
  builder_.add_start(start);
  builder_.add_color(color);
  builder_.add_isFilled(isFilled);
  builder_.add_renderType(renderType);
  return builder_.Finish();
}

inline flatbuffers::Offset<RenderMessage> CreateRenderMessageDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    RenderType renderType = RenderType_DrawLine2D,
    flatbuffers::Offset<Color> color = 0,
    const Vector3 *start = 0,
    const Vector3 *end = 0,
    int32_t scaleX = 1,
    int32_t scaleY = 1,
    const char *text = nullptr,
    bool isFilled = false) {
  return rlbot::flat::CreateRenderMessage(
      _fbb,
      renderType,
      color,
      start,
      end,
      scaleX,
      scaleY,
      text ? _fbb.CreateString(text) : 0,
      isFilled);
}

struct RenderGroup FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_RENDERMESSAGES = 4,
    VT_ID = 6
  };
  const flatbuffers::Vector<flatbuffers::Offset<RenderMessage>> *renderMessages() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<RenderMessage>> *>(VT_RENDERMESSAGES);
  }
  /// The id of the render group
  int32_t id() const {
    return GetField<int32_t>(VT_ID, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_RENDERMESSAGES) &&
           verifier.Verify(renderMessages()) &&
           verifier.VerifyVectorOfTables(renderMessages()) &&
           VerifyField<int32_t>(verifier, VT_ID) &&
           verifier.EndTable();
  }
};

struct RenderGroupBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_renderMessages(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<RenderMessage>>> renderMessages) {
    fbb_.AddOffset(RenderGroup::VT_RENDERMESSAGES, renderMessages);
  }
  void add_id(int32_t id) {
    fbb_.AddElement<int32_t>(RenderGroup::VT_ID, id, 0);
  }
  explicit RenderGroupBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  RenderGroupBuilder &operator=(const RenderGroupBuilder &);
  flatbuffers::Offset<RenderGroup> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<RenderGroup>(end);
    return o;
  }
};

inline flatbuffers::Offset<RenderGroup> CreateRenderGroup(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<RenderMessage>>> renderMessages = 0,
    int32_t id = 0) {
  RenderGroupBuilder builder_(_fbb);
  builder_.add_id(id);
  builder_.add_renderMessages(renderMessages);
  return builder_.Finish();
}

inline flatbuffers::Offset<RenderGroup> CreateRenderGroupDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<flatbuffers::Offset<RenderMessage>> *renderMessages = nullptr,
    int32_t id = 0) {
  return rlbot::flat::CreateRenderGroup(
      _fbb,
      renderMessages ? _fbb.CreateVector<flatbuffers::Offset<RenderMessage>>(*renderMessages) : 0,
      id);
}

struct QuickChat FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_QUICKCHATSELECTION = 4,
    VT_PLAYERINDEX = 6,
    VT_TEAMONLY = 8,
    VT_MESSAGEINDEX = 10,
    VT_TIMESTAMP = 12
  };
  QuickChatSelection quickChatSelection() const {
    return static_cast<QuickChatSelection>(GetField<int8_t>(VT_QUICKCHATSELECTION, 0));
  }
  /// The index of the player that sent the quick chat
  int32_t playerIndex() const {
    return GetField<int32_t>(VT_PLAYERINDEX, 0);
  }
  /// True if the chat is team only false if everyone can see it.
  bool teamOnly() const {
    return GetField<uint8_t>(VT_TEAMONLY, 0) != 0;
  }
  int32_t messageIndex() const {
    return GetField<int32_t>(VT_MESSAGEINDEX, 0);
  }
  float timeStamp() const {
    return GetField<float>(VT_TIMESTAMP, 0.0f);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_QUICKCHATSELECTION) &&
           VerifyField<int32_t>(verifier, VT_PLAYERINDEX) &&
           VerifyField<uint8_t>(verifier, VT_TEAMONLY) &&
           VerifyField<int32_t>(verifier, VT_MESSAGEINDEX) &&
           VerifyField<float>(verifier, VT_TIMESTAMP) &&
           verifier.EndTable();
  }
};

struct QuickChatBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_quickChatSelection(QuickChatSelection quickChatSelection) {
    fbb_.AddElement<int8_t>(QuickChat::VT_QUICKCHATSELECTION, static_cast<int8_t>(quickChatSelection), 0);
  }
  void add_playerIndex(int32_t playerIndex) {
    fbb_.AddElement<int32_t>(QuickChat::VT_PLAYERINDEX, playerIndex, 0);
  }
  void add_teamOnly(bool teamOnly) {
    fbb_.AddElement<uint8_t>(QuickChat::VT_TEAMONLY, static_cast<uint8_t>(teamOnly), 0);
  }
  void add_messageIndex(int32_t messageIndex) {
    fbb_.AddElement<int32_t>(QuickChat::VT_MESSAGEINDEX, messageIndex, 0);
  }
  void add_timeStamp(float timeStamp) {
    fbb_.AddElement<float>(QuickChat::VT_TIMESTAMP, timeStamp, 0.0f);
  }
  explicit QuickChatBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  QuickChatBuilder &operator=(const QuickChatBuilder &);
  flatbuffers::Offset<QuickChat> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<QuickChat>(end);
    return o;
  }
};

inline flatbuffers::Offset<QuickChat> CreateQuickChat(
    flatbuffers::FlatBufferBuilder &_fbb,
    QuickChatSelection quickChatSelection = QuickChatSelection_Information_IGotIt,
    int32_t playerIndex = 0,
    bool teamOnly = false,
    int32_t messageIndex = 0,
    float timeStamp = 0.0f) {
  QuickChatBuilder builder_(_fbb);
  builder_.add_timeStamp(timeStamp);
  builder_.add_messageIndex(messageIndex);
  builder_.add_playerIndex(playerIndex);
  builder_.add_teamOnly(teamOnly);
  builder_.add_quickChatSelection(quickChatSelection);
  return builder_.Finish();
}

/// A minimal version of player data, useful when bandwidth needs to be conserved.
struct TinyPlayer FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_LOCATION = 4,
    VT_ROTATION = 6,
    VT_VELOCITY = 8,
    VT_HASWHEELCONTACT = 10,
    VT_ISSUPERSONIC = 12,
    VT_TEAM = 14,
    VT_BOOST = 16
  };
  const Vector3 *location() const {
    return GetStruct<const Vector3 *>(VT_LOCATION);
  }
  const Rotator *rotation() const {
    return GetStruct<const Rotator *>(VT_ROTATION);
  }
  const Vector3 *velocity() const {
    return GetStruct<const Vector3 *>(VT_VELOCITY);
  }
  bool hasWheelContact() const {
    return GetField<uint8_t>(VT_HASWHEELCONTACT, 0) != 0;
  }
  bool isSupersonic() const {
    return GetField<uint8_t>(VT_ISSUPERSONIC, 0) != 0;
  }
  int32_t team() const {
    return GetField<int32_t>(VT_TEAM, 0);
  }
  int32_t boost() const {
    return GetField<int32_t>(VT_BOOST, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<Vector3>(verifier, VT_LOCATION) &&
           VerifyField<Rotator>(verifier, VT_ROTATION) &&
           VerifyField<Vector3>(verifier, VT_VELOCITY) &&
           VerifyField<uint8_t>(verifier, VT_HASWHEELCONTACT) &&
           VerifyField<uint8_t>(verifier, VT_ISSUPERSONIC) &&
           VerifyField<int32_t>(verifier, VT_TEAM) &&
           VerifyField<int32_t>(verifier, VT_BOOST) &&
           verifier.EndTable();
  }
};

struct TinyPlayerBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_location(const Vector3 *location) {
    fbb_.AddStruct(TinyPlayer::VT_LOCATION, location);
  }
  void add_rotation(const Rotator *rotation) {
    fbb_.AddStruct(TinyPlayer::VT_ROTATION, rotation);
  }
  void add_velocity(const Vector3 *velocity) {
    fbb_.AddStruct(TinyPlayer::VT_VELOCITY, velocity);
  }
  void add_hasWheelContact(bool hasWheelContact) {
    fbb_.AddElement<uint8_t>(TinyPlayer::VT_HASWHEELCONTACT, static_cast<uint8_t>(hasWheelContact), 0);
  }
  void add_isSupersonic(bool isSupersonic) {
    fbb_.AddElement<uint8_t>(TinyPlayer::VT_ISSUPERSONIC, static_cast<uint8_t>(isSupersonic), 0);
  }
  void add_team(int32_t team) {
    fbb_.AddElement<int32_t>(TinyPlayer::VT_TEAM, team, 0);
  }
  void add_boost(int32_t boost) {
    fbb_.AddElement<int32_t>(TinyPlayer::VT_BOOST, boost, 0);
  }
  explicit TinyPlayerBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  TinyPlayerBuilder &operator=(const TinyPlayerBuilder &);
  flatbuffers::Offset<TinyPlayer> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<TinyPlayer>(end);
    return o;
  }
};

inline flatbuffers::Offset<TinyPlayer> CreateTinyPlayer(
    flatbuffers::FlatBufferBuilder &_fbb,
    const Vector3 *location = 0,
    const Rotator *rotation = 0,
    const Vector3 *velocity = 0,
    bool hasWheelContact = false,
    bool isSupersonic = false,
    int32_t team = 0,
    int32_t boost = 0) {
  TinyPlayerBuilder builder_(_fbb);
  builder_.add_boost(boost);
  builder_.add_team(team);
  builder_.add_velocity(velocity);
  builder_.add_rotation(rotation);
  builder_.add_location(location);
  builder_.add_isSupersonic(isSupersonic);
  builder_.add_hasWheelContact(hasWheelContact);
  return builder_.Finish();
}

/// A minimal version of the ball, useful when bandwidth needs to be conserved.
struct TinyBall FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_LOCATION = 4,
    VT_VELOCITY = 6
  };
  const Vector3 *location() const {
    return GetStruct<const Vector3 *>(VT_LOCATION);
  }
  const Vector3 *velocity() const {
    return GetStruct<const Vector3 *>(VT_VELOCITY);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<Vector3>(verifier, VT_LOCATION) &&
           VerifyField<Vector3>(verifier, VT_VELOCITY) &&
           verifier.EndTable();
  }
};

struct TinyBallBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_location(const Vector3 *location) {
    fbb_.AddStruct(TinyBall::VT_LOCATION, location);
  }
  void add_velocity(const Vector3 *velocity) {
    fbb_.AddStruct(TinyBall::VT_VELOCITY, velocity);
  }
  explicit TinyBallBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  TinyBallBuilder &operator=(const TinyBallBuilder &);
  flatbuffers::Offset<TinyBall> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<TinyBall>(end);
    return o;
  }
};

inline flatbuffers::Offset<TinyBall> CreateTinyBall(
    flatbuffers::FlatBufferBuilder &_fbb,
    const Vector3 *location = 0,
    const Vector3 *velocity = 0) {
  TinyBallBuilder builder_(_fbb);
  builder_.add_velocity(velocity);
  builder_.add_location(location);
  return builder_.Finish();
}

/// A minimal version of the game tick packet, useful when bandwidth needs to be conserved.
struct TinyPacket FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_PLAYERS = 4,
    VT_BALL = 6
  };
  const flatbuffers::Vector<flatbuffers::Offset<TinyPlayer>> *players() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<TinyPlayer>> *>(VT_PLAYERS);
  }
  const TinyBall *ball() const {
    return GetPointer<const TinyBall *>(VT_BALL);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PLAYERS) &&
           verifier.Verify(players()) &&
           verifier.VerifyVectorOfTables(players()) &&
           VerifyOffset(verifier, VT_BALL) &&
           verifier.VerifyTable(ball()) &&
           verifier.EndTable();
  }
};

struct TinyPacketBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_players(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<TinyPlayer>>> players) {
    fbb_.AddOffset(TinyPacket::VT_PLAYERS, players);
  }
  void add_ball(flatbuffers::Offset<TinyBall> ball) {
    fbb_.AddOffset(TinyPacket::VT_BALL, ball);
  }
  explicit TinyPacketBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  TinyPacketBuilder &operator=(const TinyPacketBuilder &);
  flatbuffers::Offset<TinyPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<TinyPacket>(end);
    return o;
  }
};

inline flatbuffers::Offset<TinyPacket> CreateTinyPacket(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<TinyPlayer>>> players = 0,
    flatbuffers::Offset<TinyBall> ball = 0) {
  TinyPacketBuilder builder_(_fbb);
  builder_.add_ball(ball);
  builder_.add_players(players);
  return builder_.Finish();
}

inline flatbuffers::Offset<TinyPacket> CreateTinyPacketDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<flatbuffers::Offset<TinyPlayer>> *players = nullptr,
    flatbuffers::Offset<TinyBall> ball = 0) {
  return rlbot::flat::CreateTinyPacket(
      _fbb,
      players ? _fbb.CreateVector<flatbuffers::Offset<TinyPlayer>>(*players) : 0,
      ball);
}

struct PredictionSlice FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_GAMESECONDS = 4,
    VT_PHYSICS = 6
  };
  /// The moment in game time that this prediction corresponds to.
  /// This corresponds to 'secondsElapsed' in the GameInfo table.
  float gameSeconds() const {
    return GetField<float>(VT_GAMESECONDS, 0.0f);
  }
  /// The predicted location and motion of the object.
  const Physics *physics() const {
    return GetPointer<const Physics *>(VT_PHYSICS);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<float>(verifier, VT_GAMESECONDS) &&
           VerifyOffset(verifier, VT_PHYSICS) &&
           verifier.VerifyTable(physics()) &&
           verifier.EndTable();
  }
};

struct PredictionSliceBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_gameSeconds(float gameSeconds) {
    fbb_.AddElement<float>(PredictionSlice::VT_GAMESECONDS, gameSeconds, 0.0f);
  }
  void add_physics(flatbuffers::Offset<Physics> physics) {
    fbb_.AddOffset(PredictionSlice::VT_PHYSICS, physics);
  }
  explicit PredictionSliceBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  PredictionSliceBuilder &operator=(const PredictionSliceBuilder &);
  flatbuffers::Offset<PredictionSlice> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<PredictionSlice>(end);
    return o;
  }
};

inline flatbuffers::Offset<PredictionSlice> CreatePredictionSlice(
    flatbuffers::FlatBufferBuilder &_fbb,
    float gameSeconds = 0.0f,
    flatbuffers::Offset<Physics> physics = 0) {
  PredictionSliceBuilder builder_(_fbb);
  builder_.add_physics(physics);
  builder_.add_gameSeconds(gameSeconds);
  return builder_.Finish();
}

struct BallPrediction FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_SLICES = 4
  };
  /// A list of places the ball will be at specific times in the future.
  /// It is guaranteed to sorted so that time increases with each slice.
  /// It is NOT guaranteed to have a consistent amount of time between slices.
  const flatbuffers::Vector<flatbuffers::Offset<PredictionSlice>> *slices() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<PredictionSlice>> *>(VT_SLICES);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_SLICES) &&
           verifier.Verify(slices()) &&
           verifier.VerifyVectorOfTables(slices()) &&
           verifier.EndTable();
  }
};

struct BallPredictionBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_slices(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<PredictionSlice>>> slices) {
    fbb_.AddOffset(BallPrediction::VT_SLICES, slices);
  }
  explicit BallPredictionBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  BallPredictionBuilder &operator=(const BallPredictionBuilder &);
  flatbuffers::Offset<BallPrediction> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<BallPrediction>(end);
    return o;
  }
};

inline flatbuffers::Offset<BallPrediction> CreateBallPrediction(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<PredictionSlice>>> slices = 0) {
  BallPredictionBuilder builder_(_fbb);
  builder_.add_slices(slices);
  return builder_.Finish();
}

inline flatbuffers::Offset<BallPrediction> CreateBallPredictionDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<flatbuffers::Offset<PredictionSlice>> *slices = nullptr) {
  return rlbot::flat::CreateBallPrediction(
      _fbb,
      slices ? _fbb.CreateVector<flatbuffers::Offset<PredictionSlice>>(*slices) : 0);
}

/// A bot controlled by the RLBot framework
struct RLBotPlayer FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct RLBotPlayerBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  explicit RLBotPlayerBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  RLBotPlayerBuilder &operator=(const RLBotPlayerBuilder &);
  flatbuffers::Offset<RLBotPlayer> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<RLBotPlayer>(end);
    return o;
  }
};

inline flatbuffers::Offset<RLBotPlayer> CreateRLBotPlayer(
    flatbuffers::FlatBufferBuilder &_fbb) {
  RLBotPlayerBuilder builder_(_fbb);
  return builder_.Finish();
}

/// A normal human player
struct HumanPlayer FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct HumanPlayerBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  explicit HumanPlayerBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  HumanPlayerBuilder &operator=(const HumanPlayerBuilder &);
  flatbuffers::Offset<HumanPlayer> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<HumanPlayer>(end);
    return o;
  }
};

inline flatbuffers::Offset<HumanPlayer> CreateHumanPlayer(
    flatbuffers::FlatBufferBuilder &_fbb) {
  HumanPlayerBuilder builder_(_fbb);
  return builder_.Finish();
}

/// A psyonix bot, e.g. All Star bot
struct PsyonixBotPlayer FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_BOTSKILL = 4
  };
  float botSkill() const {
    return GetField<float>(VT_BOTSKILL, 0.0f);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<float>(verifier, VT_BOTSKILL) &&
           verifier.EndTable();
  }
};

struct PsyonixBotPlayerBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_botSkill(float botSkill) {
    fbb_.AddElement<float>(PsyonixBotPlayer::VT_BOTSKILL, botSkill, 0.0f);
  }
  explicit PsyonixBotPlayerBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  PsyonixBotPlayerBuilder &operator=(const PsyonixBotPlayerBuilder &);
  flatbuffers::Offset<PsyonixBotPlayer> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<PsyonixBotPlayer>(end);
    return o;
  }
};

inline flatbuffers::Offset<PsyonixBotPlayer> CreatePsyonixBotPlayer(
    flatbuffers::FlatBufferBuilder &_fbb,
    float botSkill = 0.0f) {
  PsyonixBotPlayerBuilder builder_(_fbb);
  builder_.add_botSkill(botSkill);
  return builder_.Finish();
}

/// A player that Rocket League treats as human, e.g. has a dedicated camera and can do training mode,
/// but is actually controlled by a bot.
struct PartyMemberBotPlayer FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           verifier.EndTable();
  }
};

struct PartyMemberBotPlayerBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  explicit PartyMemberBotPlayerBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  PartyMemberBotPlayerBuilder &operator=(const PartyMemberBotPlayerBuilder &);
  flatbuffers::Offset<PartyMemberBotPlayer> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<PartyMemberBotPlayer>(end);
    return o;
  }
};

inline flatbuffers::Offset<PartyMemberBotPlayer> CreatePartyMemberBotPlayer(
    flatbuffers::FlatBufferBuilder &_fbb) {
  PartyMemberBotPlayerBuilder builder_(_fbb);
  return builder_.Finish();
}

/// The car type, color, and other aspects of the player's appearance.
/// See https://github.com/RLBot/RLBot/wiki/Bot-Customization
struct PlayerLoadout FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_TEAMCOLORID = 4,
    VT_CUSTOMCOLORID = 6,
    VT_CARID = 8,
    VT_DECALID = 10,
    VT_WHEELSID = 12,
    VT_BOOSTID = 14,
    VT_ANTENNAID = 16,
    VT_HATID = 18,
    VT_PAINTFINISHID = 20,
    VT_CUSTOMFINISHID = 22,
    VT_ENGINEAUDIOID = 24,
    VT_TRAILSID = 26,
    VT_GOALEXPLOSIONID = 28,
    VT_LOADOUTPAINT = 30,
    VT_PRIMARYCOLORLOOKUP = 32,
    VT_SECONDARYCOLORLOOKUP = 34
  };
  int32_t teamColorId() const {
    return GetField<int32_t>(VT_TEAMCOLORID, 0);
  }
  int32_t customColorId() const {
    return GetField<int32_t>(VT_CUSTOMCOLORID, 0);
  }
  int32_t carId() const {
    return GetField<int32_t>(VT_CARID, 0);
  }
  int32_t decalId() const {
    return GetField<int32_t>(VT_DECALID, 0);
  }
  int32_t wheelsId() const {
    return GetField<int32_t>(VT_WHEELSID, 0);
  }
  int32_t boostId() const {
    return GetField<int32_t>(VT_BOOSTID, 0);
  }
  int32_t antennaId() const {
    return GetField<int32_t>(VT_ANTENNAID, 0);
  }
  int32_t hatId() const {
    return GetField<int32_t>(VT_HATID, 0);
  }
  int32_t paintFinishId() const {
    return GetField<int32_t>(VT_PAINTFINISHID, 0);
  }
  int32_t customFinishId() const {
    return GetField<int32_t>(VT_CUSTOMFINISHID, 0);
  }
  int32_t engineAudioId() const {
    return GetField<int32_t>(VT_ENGINEAUDIOID, 0);
  }
  int32_t trailsId() const {
    return GetField<int32_t>(VT_TRAILSID, 0);
  }
  int32_t goalExplosionId() const {
    return GetField<int32_t>(VT_GOALEXPLOSIONID, 0);
  }
  const LoadoutPaint *loadoutPaint() const {
    return GetPointer<const LoadoutPaint *>(VT_LOADOUTPAINT);
  }
  /// Sets the primary color of the car to the swatch that most closely matches the provided
  /// RGB color value. If set, this overrides teamColorId.
  const Color *primaryColorLookup() const {
    return GetPointer<const Color *>(VT_PRIMARYCOLORLOOKUP);
  }
  /// Sets the secondary color of the car to the swatch that most closely matches the provided
  /// RGB color value. If set, this overrides customColorId.
  const Color *secondaryColorLookup() const {
    return GetPointer<const Color *>(VT_SECONDARYCOLORLOOKUP);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_TEAMCOLORID) &&
           VerifyField<int32_t>(verifier, VT_CUSTOMCOLORID) &&
           VerifyField<int32_t>(verifier, VT_CARID) &&
           VerifyField<int32_t>(verifier, VT_DECALID) &&
           VerifyField<int32_t>(verifier, VT_WHEELSID) &&
           VerifyField<int32_t>(verifier, VT_BOOSTID) &&
           VerifyField<int32_t>(verifier, VT_ANTENNAID) &&
           VerifyField<int32_t>(verifier, VT_HATID) &&
           VerifyField<int32_t>(verifier, VT_PAINTFINISHID) &&
           VerifyField<int32_t>(verifier, VT_CUSTOMFINISHID) &&
           VerifyField<int32_t>(verifier, VT_ENGINEAUDIOID) &&
           VerifyField<int32_t>(verifier, VT_TRAILSID) &&
           VerifyField<int32_t>(verifier, VT_GOALEXPLOSIONID) &&
           VerifyOffset(verifier, VT_LOADOUTPAINT) &&
           verifier.VerifyTable(loadoutPaint()) &&
           VerifyOffset(verifier, VT_PRIMARYCOLORLOOKUP) &&
           verifier.VerifyTable(primaryColorLookup()) &&
           VerifyOffset(verifier, VT_SECONDARYCOLORLOOKUP) &&
           verifier.VerifyTable(secondaryColorLookup()) &&
           verifier.EndTable();
  }
};

struct PlayerLoadoutBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_teamColorId(int32_t teamColorId) {
    fbb_.AddElement<int32_t>(PlayerLoadout::VT_TEAMCOLORID, teamColorId, 0);
  }
  void add_customColorId(int32_t customColorId) {
    fbb_.AddElement<int32_t>(PlayerLoadout::VT_CUSTOMCOLORID, customColorId, 0);
  }
  void add_carId(int32_t carId) {
    fbb_.AddElement<int32_t>(PlayerLoadout::VT_CARID, carId, 0);
  }
  void add_decalId(int32_t decalId) {
    fbb_.AddElement<int32_t>(PlayerLoadout::VT_DECALID, decalId, 0);
  }
  void add_wheelsId(int32_t wheelsId) {
    fbb_.AddElement<int32_t>(PlayerLoadout::VT_WHEELSID, wheelsId, 0);
  }
  void add_boostId(int32_t boostId) {
    fbb_.AddElement<int32_t>(PlayerLoadout::VT_BOOSTID, boostId, 0);
  }
  void add_antennaId(int32_t antennaId) {
    fbb_.AddElement<int32_t>(PlayerLoadout::VT_ANTENNAID, antennaId, 0);
  }
  void add_hatId(int32_t hatId) {
    fbb_.AddElement<int32_t>(PlayerLoadout::VT_HATID, hatId, 0);
  }
  void add_paintFinishId(int32_t paintFinishId) {
    fbb_.AddElement<int32_t>(PlayerLoadout::VT_PAINTFINISHID, paintFinishId, 0);
  }
  void add_customFinishId(int32_t customFinishId) {
    fbb_.AddElement<int32_t>(PlayerLoadout::VT_CUSTOMFINISHID, customFinishId, 0);
  }
  void add_engineAudioId(int32_t engineAudioId) {
    fbb_.AddElement<int32_t>(PlayerLoadout::VT_ENGINEAUDIOID, engineAudioId, 0);
  }
  void add_trailsId(int32_t trailsId) {
    fbb_.AddElement<int32_t>(PlayerLoadout::VT_TRAILSID, trailsId, 0);
  }
  void add_goalExplosionId(int32_t goalExplosionId) {
    fbb_.AddElement<int32_t>(PlayerLoadout::VT_GOALEXPLOSIONID, goalExplosionId, 0);
  }
  void add_loadoutPaint(flatbuffers::Offset<LoadoutPaint> loadoutPaint) {
    fbb_.AddOffset(PlayerLoadout::VT_LOADOUTPAINT, loadoutPaint);
  }
  void add_primaryColorLookup(flatbuffers::Offset<Color> primaryColorLookup) {
    fbb_.AddOffset(PlayerLoadout::VT_PRIMARYCOLORLOOKUP, primaryColorLookup);
  }
  void add_secondaryColorLookup(flatbuffers::Offset<Color> secondaryColorLookup) {
    fbb_.AddOffset(PlayerLoadout::VT_SECONDARYCOLORLOOKUP, secondaryColorLookup);
  }
  explicit PlayerLoadoutBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  PlayerLoadoutBuilder &operator=(const PlayerLoadoutBuilder &);
  flatbuffers::Offset<PlayerLoadout> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<PlayerLoadout>(end);
    return o;
  }
};

inline flatbuffers::Offset<PlayerLoadout> CreatePlayerLoadout(
    flatbuffers::FlatBufferBuilder &_fbb,
    int32_t teamColorId = 0,
    int32_t customColorId = 0,
    int32_t carId = 0,
    int32_t decalId = 0,
    int32_t wheelsId = 0,
    int32_t boostId = 0,
    int32_t antennaId = 0,
    int32_t hatId = 0,
    int32_t paintFinishId = 0,
    int32_t customFinishId = 0,
    int32_t engineAudioId = 0,
    int32_t trailsId = 0,
    int32_t goalExplosionId = 0,
    flatbuffers::Offset<LoadoutPaint> loadoutPaint = 0,
    flatbuffers::Offset<Color> primaryColorLookup = 0,
    flatbuffers::Offset<Color> secondaryColorLookup = 0) {
  PlayerLoadoutBuilder builder_(_fbb);
  builder_.add_secondaryColorLookup(secondaryColorLookup);
  builder_.add_primaryColorLookup(primaryColorLookup);
  builder_.add_loadoutPaint(loadoutPaint);
  builder_.add_goalExplosionId(goalExplosionId);
  builder_.add_trailsId(trailsId);
  builder_.add_engineAudioId(engineAudioId);
  builder_.add_customFinishId(customFinishId);
  builder_.add_paintFinishId(paintFinishId);
  builder_.add_hatId(hatId);
  builder_.add_antennaId(antennaId);
  builder_.add_boostId(boostId);
  builder_.add_wheelsId(wheelsId);
  builder_.add_decalId(decalId);
  builder_.add_carId(carId);
  builder_.add_customColorId(customColorId);
  builder_.add_teamColorId(teamColorId);
  return builder_.Finish();
}

/// Specification for 'painted' items. See https://github.com/RLBot/RLBot/wiki/Bot-Customization
struct LoadoutPaint FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_CARPAINTID = 4,
    VT_DECALPAINTID = 6,
    VT_WHEELSPAINTID = 8,
    VT_BOOSTPAINTID = 10,
    VT_ANTENNAPAINTID = 12,
    VT_HATPAINTID = 14,
    VT_TRAILSPAINTID = 16,
    VT_GOALEXPLOSIONPAINTID = 18
  };
  int32_t carPaintId() const {
    return GetField<int32_t>(VT_CARPAINTID, 0);
  }
  int32_t decalPaintId() const {
    return GetField<int32_t>(VT_DECALPAINTID, 0);
  }
  int32_t wheelsPaintId() const {
    return GetField<int32_t>(VT_WHEELSPAINTID, 0);
  }
  int32_t boostPaintId() const {
    return GetField<int32_t>(VT_BOOSTPAINTID, 0);
  }
  int32_t antennaPaintId() const {
    return GetField<int32_t>(VT_ANTENNAPAINTID, 0);
  }
  int32_t hatPaintId() const {
    return GetField<int32_t>(VT_HATPAINTID, 0);
  }
  int32_t trailsPaintId() const {
    return GetField<int32_t>(VT_TRAILSPAINTID, 0);
  }
  int32_t goalExplosionPaintId() const {
    return GetField<int32_t>(VT_GOALEXPLOSIONPAINTID, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_CARPAINTID) &&
           VerifyField<int32_t>(verifier, VT_DECALPAINTID) &&
           VerifyField<int32_t>(verifier, VT_WHEELSPAINTID) &&
           VerifyField<int32_t>(verifier, VT_BOOSTPAINTID) &&
           VerifyField<int32_t>(verifier, VT_ANTENNAPAINTID) &&
           VerifyField<int32_t>(verifier, VT_HATPAINTID) &&
           VerifyField<int32_t>(verifier, VT_TRAILSPAINTID) &&
           VerifyField<int32_t>(verifier, VT_GOALEXPLOSIONPAINTID) &&
           verifier.EndTable();
  }
};

struct LoadoutPaintBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_carPaintId(int32_t carPaintId) {
    fbb_.AddElement<int32_t>(LoadoutPaint::VT_CARPAINTID, carPaintId, 0);
  }
  void add_decalPaintId(int32_t decalPaintId) {
    fbb_.AddElement<int32_t>(LoadoutPaint::VT_DECALPAINTID, decalPaintId, 0);
  }
  void add_wheelsPaintId(int32_t wheelsPaintId) {
    fbb_.AddElement<int32_t>(LoadoutPaint::VT_WHEELSPAINTID, wheelsPaintId, 0);
  }
  void add_boostPaintId(int32_t boostPaintId) {
    fbb_.AddElement<int32_t>(LoadoutPaint::VT_BOOSTPAINTID, boostPaintId, 0);
  }
  void add_antennaPaintId(int32_t antennaPaintId) {
    fbb_.AddElement<int32_t>(LoadoutPaint::VT_ANTENNAPAINTID, antennaPaintId, 0);
  }
  void add_hatPaintId(int32_t hatPaintId) {
    fbb_.AddElement<int32_t>(LoadoutPaint::VT_HATPAINTID, hatPaintId, 0);
  }
  void add_trailsPaintId(int32_t trailsPaintId) {
    fbb_.AddElement<int32_t>(LoadoutPaint::VT_TRAILSPAINTID, trailsPaintId, 0);
  }
  void add_goalExplosionPaintId(int32_t goalExplosionPaintId) {
    fbb_.AddElement<int32_t>(LoadoutPaint::VT_GOALEXPLOSIONPAINTID, goalExplosionPaintId, 0);
  }
  explicit LoadoutPaintBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  LoadoutPaintBuilder &operator=(const LoadoutPaintBuilder &);
  flatbuffers::Offset<LoadoutPaint> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<LoadoutPaint>(end);
    return o;
  }
};

inline flatbuffers::Offset<LoadoutPaint> CreateLoadoutPaint(
    flatbuffers::FlatBufferBuilder &_fbb,
    int32_t carPaintId = 0,
    int32_t decalPaintId = 0,
    int32_t wheelsPaintId = 0,
    int32_t boostPaintId = 0,
    int32_t antennaPaintId = 0,
    int32_t hatPaintId = 0,
    int32_t trailsPaintId = 0,
    int32_t goalExplosionPaintId = 0) {
  LoadoutPaintBuilder builder_(_fbb);
  builder_.add_goalExplosionPaintId(goalExplosionPaintId);
  builder_.add_trailsPaintId(trailsPaintId);
  builder_.add_hatPaintId(hatPaintId);
  builder_.add_antennaPaintId(antennaPaintId);
  builder_.add_boostPaintId(boostPaintId);
  builder_.add_wheelsPaintId(wheelsPaintId);
  builder_.add_decalPaintId(decalPaintId);
  builder_.add_carPaintId(carPaintId);
  return builder_.Finish();
}

struct PlayerConfiguration FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_VARIETY_TYPE = 4,
    VT_VARIETY = 6,
    VT_NAME = 8,
    VT_TEAM = 10,
    VT_LOADOUT = 12,
    VT_SPAWNID = 14
  };
  PlayerClass variety_type() const {
    return static_cast<PlayerClass>(GetField<uint8_t>(VT_VARIETY_TYPE, 0));
  }
  const void *variety() const {
    return GetPointer<const void *>(VT_VARIETY);
  }
  template<typename T> const T *variety_as() const;
  const RLBotPlayer *variety_as_RLBotPlayer() const {
    return variety_type() == PlayerClass_RLBotPlayer ? static_cast<const RLBotPlayer *>(variety()) : nullptr;
  }
  const HumanPlayer *variety_as_HumanPlayer() const {
    return variety_type() == PlayerClass_HumanPlayer ? static_cast<const HumanPlayer *>(variety()) : nullptr;
  }
  const PsyonixBotPlayer *variety_as_PsyonixBotPlayer() const {
    return variety_type() == PlayerClass_PsyonixBotPlayer ? static_cast<const PsyonixBotPlayer *>(variety()) : nullptr;
  }
  const PartyMemberBotPlayer *variety_as_PartyMemberBotPlayer() const {
    return variety_type() == PlayerClass_PartyMemberBotPlayer ? static_cast<const PartyMemberBotPlayer *>(variety()) : nullptr;
  }
  const flatbuffers::String *name() const {
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  int32_t team() const {
    return GetField<int32_t>(VT_TEAM, 0);
  }
  const PlayerLoadout *loadout() const {
    return GetPointer<const PlayerLoadout *>(VT_LOADOUT);
  }
  /// In the case where the requested player index is not available, spawnId will help
  /// the framework figure out what index was actually assigned to this player instead.
  int32_t spawnId() const {
    return GetField<int32_t>(VT_SPAWNID, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_VARIETY_TYPE) &&
           VerifyOffset(verifier, VT_VARIETY) &&
           VerifyPlayerClass(verifier, variety(), variety_type()) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.Verify(name()) &&
           VerifyField<int32_t>(verifier, VT_TEAM) &&
           VerifyOffset(verifier, VT_LOADOUT) &&
           verifier.VerifyTable(loadout()) &&
           VerifyField<int32_t>(verifier, VT_SPAWNID) &&
           verifier.EndTable();
  }
};

template<> inline const RLBotPlayer *PlayerConfiguration::variety_as<RLBotPlayer>() const {
  return variety_as_RLBotPlayer();
}

template<> inline const HumanPlayer *PlayerConfiguration::variety_as<HumanPlayer>() const {
  return variety_as_HumanPlayer();
}

template<> inline const PsyonixBotPlayer *PlayerConfiguration::variety_as<PsyonixBotPlayer>() const {
  return variety_as_PsyonixBotPlayer();
}

template<> inline const PartyMemberBotPlayer *PlayerConfiguration::variety_as<PartyMemberBotPlayer>() const {
  return variety_as_PartyMemberBotPlayer();
}

struct PlayerConfigurationBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_variety_type(PlayerClass variety_type) {
    fbb_.AddElement<uint8_t>(PlayerConfiguration::VT_VARIETY_TYPE, static_cast<uint8_t>(variety_type), 0);
  }
  void add_variety(flatbuffers::Offset<void> variety) {
    fbb_.AddOffset(PlayerConfiguration::VT_VARIETY, variety);
  }
  void add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(PlayerConfiguration::VT_NAME, name);
  }
  void add_team(int32_t team) {
    fbb_.AddElement<int32_t>(PlayerConfiguration::VT_TEAM, team, 0);
  }
  void add_loadout(flatbuffers::Offset<PlayerLoadout> loadout) {
    fbb_.AddOffset(PlayerConfiguration::VT_LOADOUT, loadout);
  }
  void add_spawnId(int32_t spawnId) {
    fbb_.AddElement<int32_t>(PlayerConfiguration::VT_SPAWNID, spawnId, 0);
  }
  explicit PlayerConfigurationBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  PlayerConfigurationBuilder &operator=(const PlayerConfigurationBuilder &);
  flatbuffers::Offset<PlayerConfiguration> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<PlayerConfiguration>(end);
    return o;
  }
};

inline flatbuffers::Offset<PlayerConfiguration> CreatePlayerConfiguration(
    flatbuffers::FlatBufferBuilder &_fbb,
    PlayerClass variety_type = PlayerClass_NONE,
    flatbuffers::Offset<void> variety = 0,
    flatbuffers::Offset<flatbuffers::String> name = 0,
    int32_t team = 0,
    flatbuffers::Offset<PlayerLoadout> loadout = 0,
    int32_t spawnId = 0) {
  PlayerConfigurationBuilder builder_(_fbb);
  builder_.add_spawnId(spawnId);
  builder_.add_loadout(loadout);
  builder_.add_team(team);
  builder_.add_name(name);
  builder_.add_variety(variety);
  builder_.add_variety_type(variety_type);
  return builder_.Finish();
}

inline flatbuffers::Offset<PlayerConfiguration> CreatePlayerConfigurationDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    PlayerClass variety_type = PlayerClass_NONE,
    flatbuffers::Offset<void> variety = 0,
    const char *name = nullptr,
    int32_t team = 0,
    flatbuffers::Offset<PlayerLoadout> loadout = 0,
    int32_t spawnId = 0) {
  return rlbot::flat::CreatePlayerConfiguration(
      _fbb,
      variety_type,
      variety,
      name ? _fbb.CreateString(name) : 0,
      team,
      loadout,
      spawnId);
}

struct MutatorSettings FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_MATCHLENGTH = 4,
    VT_MAXSCORE = 6,
    VT_OVERTIMEOPTION = 8,
    VT_SERIESLENGTHOPTION = 10,
    VT_GAMESPEEDOPTION = 12,
    VT_BALLMAXSPEEDOPTION = 14,
    VT_BALLTYPEOPTION = 16,
    VT_BALLWEIGHTOPTION = 18,
    VT_BALLSIZEOPTION = 20,
    VT_BALLBOUNCINESSOPTION = 22,
    VT_BOOSTOPTION = 24,
    VT_RUMBLEOPTION = 26,
    VT_BOOSTSTRENGTHOPTION = 28,
    VT_GRAVITYOPTION = 30,
    VT_DEMOLISHOPTION = 32,
    VT_RESPAWNTIMEOPTION = 34
  };
  MatchLength matchLength() const {
    return static_cast<MatchLength>(GetField<int8_t>(VT_MATCHLENGTH, 0));
  }
  MaxScore maxScore() const {
    return static_cast<MaxScore>(GetField<int8_t>(VT_MAXSCORE, 0));
  }
  OvertimeOption overtimeOption() const {
    return static_cast<OvertimeOption>(GetField<int8_t>(VT_OVERTIMEOPTION, 0));
  }
  SeriesLengthOption seriesLengthOption() const {
    return static_cast<SeriesLengthOption>(GetField<int8_t>(VT_SERIESLENGTHOPTION, 0));
  }
  GameSpeedOption gameSpeedOption() const {
    return static_cast<GameSpeedOption>(GetField<int8_t>(VT_GAMESPEEDOPTION, 0));
  }
  BallMaxSpeedOption ballMaxSpeedOption() const {
    return static_cast<BallMaxSpeedOption>(GetField<int8_t>(VT_BALLMAXSPEEDOPTION, 0));
  }
  BallTypeOption ballTypeOption() const {
    return static_cast<BallTypeOption>(GetField<int8_t>(VT_BALLTYPEOPTION, 0));
  }
  BallWeightOption ballWeightOption() const {
    return static_cast<BallWeightOption>(GetField<int8_t>(VT_BALLWEIGHTOPTION, 0));
  }
  BallSizeOption ballSizeOption() const {
    return static_cast<BallSizeOption>(GetField<int8_t>(VT_BALLSIZEOPTION, 0));
  }
  BallBouncinessOption ballBouncinessOption() const {
    return static_cast<BallBouncinessOption>(GetField<int8_t>(VT_BALLBOUNCINESSOPTION, 0));
  }
  BoostOption boostOption() const {
    return static_cast<BoostOption>(GetField<int8_t>(VT_BOOSTOPTION, 0));
  }
  RumbleOption rumbleOption() const {
    return static_cast<RumbleOption>(GetField<int8_t>(VT_RUMBLEOPTION, 0));
  }
  BoostStrengthOption boostStrengthOption() const {
    return static_cast<BoostStrengthOption>(GetField<int8_t>(VT_BOOSTSTRENGTHOPTION, 0));
  }
  GravityOption gravityOption() const {
    return static_cast<GravityOption>(GetField<int8_t>(VT_GRAVITYOPTION, 0));
  }
  DemolishOption demolishOption() const {
    return static_cast<DemolishOption>(GetField<int8_t>(VT_DEMOLISHOPTION, 0));
  }
  RespawnTimeOption respawnTimeOption() const {
    return static_cast<RespawnTimeOption>(GetField<int8_t>(VT_RESPAWNTIMEOPTION, 0));
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_MATCHLENGTH) &&
           VerifyField<int8_t>(verifier, VT_MAXSCORE) &&
           VerifyField<int8_t>(verifier, VT_OVERTIMEOPTION) &&
           VerifyField<int8_t>(verifier, VT_SERIESLENGTHOPTION) &&
           VerifyField<int8_t>(verifier, VT_GAMESPEEDOPTION) &&
           VerifyField<int8_t>(verifier, VT_BALLMAXSPEEDOPTION) &&
           VerifyField<int8_t>(verifier, VT_BALLTYPEOPTION) &&
           VerifyField<int8_t>(verifier, VT_BALLWEIGHTOPTION) &&
           VerifyField<int8_t>(verifier, VT_BALLSIZEOPTION) &&
           VerifyField<int8_t>(verifier, VT_BALLBOUNCINESSOPTION) &&
           VerifyField<int8_t>(verifier, VT_BOOSTOPTION) &&
           VerifyField<int8_t>(verifier, VT_RUMBLEOPTION) &&
           VerifyField<int8_t>(verifier, VT_BOOSTSTRENGTHOPTION) &&
           VerifyField<int8_t>(verifier, VT_GRAVITYOPTION) &&
           VerifyField<int8_t>(verifier, VT_DEMOLISHOPTION) &&
           VerifyField<int8_t>(verifier, VT_RESPAWNTIMEOPTION) &&
           verifier.EndTable();
  }
};

struct MutatorSettingsBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_matchLength(MatchLength matchLength) {
    fbb_.AddElement<int8_t>(MutatorSettings::VT_MATCHLENGTH, static_cast<int8_t>(matchLength), 0);
  }
  void add_maxScore(MaxScore maxScore) {
    fbb_.AddElement<int8_t>(MutatorSettings::VT_MAXSCORE, static_cast<int8_t>(maxScore), 0);
  }
  void add_overtimeOption(OvertimeOption overtimeOption) {
    fbb_.AddElement<int8_t>(MutatorSettings::VT_OVERTIMEOPTION, static_cast<int8_t>(overtimeOption), 0);
  }
  void add_seriesLengthOption(SeriesLengthOption seriesLengthOption) {
    fbb_.AddElement<int8_t>(MutatorSettings::VT_SERIESLENGTHOPTION, static_cast<int8_t>(seriesLengthOption), 0);
  }
  void add_gameSpeedOption(GameSpeedOption gameSpeedOption) {
    fbb_.AddElement<int8_t>(MutatorSettings::VT_GAMESPEEDOPTION, static_cast<int8_t>(gameSpeedOption), 0);
  }
  void add_ballMaxSpeedOption(BallMaxSpeedOption ballMaxSpeedOption) {
    fbb_.AddElement<int8_t>(MutatorSettings::VT_BALLMAXSPEEDOPTION, static_cast<int8_t>(ballMaxSpeedOption), 0);
  }
  void add_ballTypeOption(BallTypeOption ballTypeOption) {
    fbb_.AddElement<int8_t>(MutatorSettings::VT_BALLTYPEOPTION, static_cast<int8_t>(ballTypeOption), 0);
  }
  void add_ballWeightOption(BallWeightOption ballWeightOption) {
    fbb_.AddElement<int8_t>(MutatorSettings::VT_BALLWEIGHTOPTION, static_cast<int8_t>(ballWeightOption), 0);
  }
  void add_ballSizeOption(BallSizeOption ballSizeOption) {
    fbb_.AddElement<int8_t>(MutatorSettings::VT_BALLSIZEOPTION, static_cast<int8_t>(ballSizeOption), 0);
  }
  void add_ballBouncinessOption(BallBouncinessOption ballBouncinessOption) {
    fbb_.AddElement<int8_t>(MutatorSettings::VT_BALLBOUNCINESSOPTION, static_cast<int8_t>(ballBouncinessOption), 0);
  }
  void add_boostOption(BoostOption boostOption) {
    fbb_.AddElement<int8_t>(MutatorSettings::VT_BOOSTOPTION, static_cast<int8_t>(boostOption), 0);
  }
  void add_rumbleOption(RumbleOption rumbleOption) {
    fbb_.AddElement<int8_t>(MutatorSettings::VT_RUMBLEOPTION, static_cast<int8_t>(rumbleOption), 0);
  }
  void add_boostStrengthOption(BoostStrengthOption boostStrengthOption) {
    fbb_.AddElement<int8_t>(MutatorSettings::VT_BOOSTSTRENGTHOPTION, static_cast<int8_t>(boostStrengthOption), 0);
  }
  void add_gravityOption(GravityOption gravityOption) {
    fbb_.AddElement<int8_t>(MutatorSettings::VT_GRAVITYOPTION, static_cast<int8_t>(gravityOption), 0);
  }
  void add_demolishOption(DemolishOption demolishOption) {
    fbb_.AddElement<int8_t>(MutatorSettings::VT_DEMOLISHOPTION, static_cast<int8_t>(demolishOption), 0);
  }
  void add_respawnTimeOption(RespawnTimeOption respawnTimeOption) {
    fbb_.AddElement<int8_t>(MutatorSettings::VT_RESPAWNTIMEOPTION, static_cast<int8_t>(respawnTimeOption), 0);
  }
  explicit MutatorSettingsBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  MutatorSettingsBuilder &operator=(const MutatorSettingsBuilder &);
  flatbuffers::Offset<MutatorSettings> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<MutatorSettings>(end);
    return o;
  }
};

inline flatbuffers::Offset<MutatorSettings> CreateMutatorSettings(
    flatbuffers::FlatBufferBuilder &_fbb,
    MatchLength matchLength = MatchLength_Five_Minutes,
    MaxScore maxScore = MaxScore_Unlimited,
    OvertimeOption overtimeOption = OvertimeOption_Unlimited,
    SeriesLengthOption seriesLengthOption = SeriesLengthOption_Unlimited,
    GameSpeedOption gameSpeedOption = GameSpeedOption_Default,
    BallMaxSpeedOption ballMaxSpeedOption = BallMaxSpeedOption_Default,
    BallTypeOption ballTypeOption = BallTypeOption_Default,
    BallWeightOption ballWeightOption = BallWeightOption_Default,
    BallSizeOption ballSizeOption = BallSizeOption_Default,
    BallBouncinessOption ballBouncinessOption = BallBouncinessOption_Default,
    BoostOption boostOption = BoostOption_Normal_Boost,
    RumbleOption rumbleOption = RumbleOption_No_Rumble,
    BoostStrengthOption boostStrengthOption = BoostStrengthOption_One,
    GravityOption gravityOption = GravityOption_Default,
    DemolishOption demolishOption = DemolishOption_Default,
    RespawnTimeOption respawnTimeOption = RespawnTimeOption_Three_Seconds) {
  MutatorSettingsBuilder builder_(_fbb);
  builder_.add_respawnTimeOption(respawnTimeOption);
  builder_.add_demolishOption(demolishOption);
  builder_.add_gravityOption(gravityOption);
  builder_.add_boostStrengthOption(boostStrengthOption);
  builder_.add_rumbleOption(rumbleOption);
  builder_.add_boostOption(boostOption);
  builder_.add_ballBouncinessOption(ballBouncinessOption);
  builder_.add_ballSizeOption(ballSizeOption);
  builder_.add_ballWeightOption(ballWeightOption);
  builder_.add_ballTypeOption(ballTypeOption);
  builder_.add_ballMaxSpeedOption(ballMaxSpeedOption);
  builder_.add_gameSpeedOption(gameSpeedOption);
  builder_.add_seriesLengthOption(seriesLengthOption);
  builder_.add_overtimeOption(overtimeOption);
  builder_.add_maxScore(maxScore);
  builder_.add_matchLength(matchLength);
  return builder_.Finish();
}

struct MatchSettings FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_PLAYERCONFIGURATIONS = 4,
    VT_GAMEMODE = 6,
    VT_GAMEMAP = 8,
    VT_SKIPREPLAYS = 10,
    VT_INSTANTSTART = 12,
    VT_MUTATORSETTINGS = 14,
    VT_EXISTINGMATCHBEHAVIOR = 16,
    VT_ENABLELOCKSTEP = 18,
    VT_ENABLERENDERING = 20,
    VT_ENABLESTATESETTING = 22,
    VT_AUTOSAVEREPLAY = 24,
    VT_GAMEMAPUPK = 26
  };
  const flatbuffers::Vector<flatbuffers::Offset<PlayerConfiguration>> *playerConfigurations() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<PlayerConfiguration>> *>(VT_PLAYERCONFIGURATIONS);
  }
  GameMode gameMode() const {
    return static_cast<GameMode>(GetField<int8_t>(VT_GAMEMODE, 0));
  }
  GameMap gameMap() const {
    return static_cast<GameMap>(GetField<int8_t>(VT_GAMEMAP, 0));
  }
  bool skipReplays() const {
    return GetField<uint8_t>(VT_SKIPREPLAYS, 0) != 0;
  }
  bool instantStart() const {
    return GetField<uint8_t>(VT_INSTANTSTART, 0) != 0;
  }
  const MutatorSettings *mutatorSettings() const {
    return GetPointer<const MutatorSettings *>(VT_MUTATORSETTINGS);
  }
  ExistingMatchBehavior existingMatchBehavior() const {
    return static_cast<ExistingMatchBehavior>(GetField<int8_t>(VT_EXISTINGMATCHBEHAVIOR, 0));
  }
  bool enableLockstep() const {
    return GetField<uint8_t>(VT_ENABLELOCKSTEP, 0) != 0;
  }
  bool enableRendering() const {
    return GetField<uint8_t>(VT_ENABLERENDERING, 0) != 0;
  }
  bool enableStateSetting() const {
    return GetField<uint8_t>(VT_ENABLESTATESETTING, 0) != 0;
  }
  bool autoSaveReplay() const {
    return GetField<uint8_t>(VT_AUTOSAVEREPLAY, 0) != 0;
  }
  /// The name of a upk file, like UtopiaStadium_P, which should be loaded.
  /// If specified, this overrides gameMap. On Steam version of Rocket League,
  /// this can be used to load custom map files, but on Epic version it only
  /// works on the Psyonix maps. Still useful because maintaining the gameMap
  /// enum as new Psyonix maps are added is annoying.
  const flatbuffers::String *gameMapUpk() const {
    return GetPointer<const flatbuffers::String *>(VT_GAMEMAPUPK);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PLAYERCONFIGURATIONS) &&
           verifier.Verify(playerConfigurations()) &&
           verifier.VerifyVectorOfTables(playerConfigurations()) &&
           VerifyField<int8_t>(verifier, VT_GAMEMODE) &&
           VerifyField<int8_t>(verifier, VT_GAMEMAP) &&
           VerifyField<uint8_t>(verifier, VT_SKIPREPLAYS) &&
           VerifyField<uint8_t>(verifier, VT_INSTANTSTART) &&
           VerifyOffset(verifier, VT_MUTATORSETTINGS) &&
           verifier.VerifyTable(mutatorSettings()) &&
           VerifyField<int8_t>(verifier, VT_EXISTINGMATCHBEHAVIOR) &&
           VerifyField<uint8_t>(verifier, VT_ENABLELOCKSTEP) &&
           VerifyField<uint8_t>(verifier, VT_ENABLERENDERING) &&
           VerifyField<uint8_t>(verifier, VT_ENABLESTATESETTING) &&
           VerifyField<uint8_t>(verifier, VT_AUTOSAVEREPLAY) &&
           VerifyOffset(verifier, VT_GAMEMAPUPK) &&
           verifier.Verify(gameMapUpk()) &&
           verifier.EndTable();
  }
};

struct MatchSettingsBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_playerConfigurations(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<PlayerConfiguration>>> playerConfigurations) {
    fbb_.AddOffset(MatchSettings::VT_PLAYERCONFIGURATIONS, playerConfigurations);
  }
  void add_gameMode(GameMode gameMode) {
    fbb_.AddElement<int8_t>(MatchSettings::VT_GAMEMODE, static_cast<int8_t>(gameMode), 0);
  }
  void add_gameMap(GameMap gameMap) {
    fbb_.AddElement<int8_t>(MatchSettings::VT_GAMEMAP, static_cast<int8_t>(gameMap), 0);
  }
  void add_skipReplays(bool skipReplays) {
    fbb_.AddElement<uint8_t>(MatchSettings::VT_SKIPREPLAYS, static_cast<uint8_t>(skipReplays), 0);
  }
  void add_instantStart(bool instantStart) {
    fbb_.AddElement<uint8_t>(MatchSettings::VT_INSTANTSTART, static_cast<uint8_t>(instantStart), 0);
  }
  void add_mutatorSettings(flatbuffers::Offset<MutatorSettings> mutatorSettings) {
    fbb_.AddOffset(MatchSettings::VT_MUTATORSETTINGS, mutatorSettings);
  }
  void add_existingMatchBehavior(ExistingMatchBehavior existingMatchBehavior) {
    fbb_.AddElement<int8_t>(MatchSettings::VT_EXISTINGMATCHBEHAVIOR, static_cast<int8_t>(existingMatchBehavior), 0);
  }
  void add_enableLockstep(bool enableLockstep) {
    fbb_.AddElement<uint8_t>(MatchSettings::VT_ENABLELOCKSTEP, static_cast<uint8_t>(enableLockstep), 0);
  }
  void add_enableRendering(bool enableRendering) {
    fbb_.AddElement<uint8_t>(MatchSettings::VT_ENABLERENDERING, static_cast<uint8_t>(enableRendering), 0);
  }
  void add_enableStateSetting(bool enableStateSetting) {
    fbb_.AddElement<uint8_t>(MatchSettings::VT_ENABLESTATESETTING, static_cast<uint8_t>(enableStateSetting), 0);
  }
  void add_autoSaveReplay(bool autoSaveReplay) {
    fbb_.AddElement<uint8_t>(MatchSettings::VT_AUTOSAVEREPLAY, static_cast<uint8_t>(autoSaveReplay), 0);
  }
  void add_gameMapUpk(flatbuffers::Offset<flatbuffers::String> gameMapUpk) {
    fbb_.AddOffset(MatchSettings::VT_GAMEMAPUPK, gameMapUpk);
  }
  explicit MatchSettingsBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  MatchSettingsBuilder &operator=(const MatchSettingsBuilder &);
  flatbuffers::Offset<MatchSettings> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<MatchSettings>(end);
    return o;
  }
};

inline flatbuffers::Offset<MatchSettings> CreateMatchSettings(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<PlayerConfiguration>>> playerConfigurations = 0,
    GameMode gameMode = GameMode_Soccer,
    GameMap gameMap = GameMap_DFHStadium,
    bool skipReplays = false,
    bool instantStart = false,
    flatbuffers::Offset<MutatorSettings> mutatorSettings = 0,
    ExistingMatchBehavior existingMatchBehavior = ExistingMatchBehavior_Restart_If_Different,
    bool enableLockstep = false,
    bool enableRendering = false,
    bool enableStateSetting = false,
    bool autoSaveReplay = false,
    flatbuffers::Offset<flatbuffers::String> gameMapUpk = 0) {
  MatchSettingsBuilder builder_(_fbb);
  builder_.add_gameMapUpk(gameMapUpk);
  builder_.add_mutatorSettings(mutatorSettings);
  builder_.add_playerConfigurations(playerConfigurations);
  builder_.add_autoSaveReplay(autoSaveReplay);
  builder_.add_enableStateSetting(enableStateSetting);
  builder_.add_enableRendering(enableRendering);
  builder_.add_enableLockstep(enableLockstep);
  builder_.add_existingMatchBehavior(existingMatchBehavior);
  builder_.add_instantStart(instantStart);
  builder_.add_skipReplays(skipReplays);
  builder_.add_gameMap(gameMap);
  builder_.add_gameMode(gameMode);
  return builder_.Finish();
}

inline flatbuffers::Offset<MatchSettings> CreateMatchSettingsDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<flatbuffers::Offset<PlayerConfiguration>> *playerConfigurations = nullptr,
    GameMode gameMode = GameMode_Soccer,
    GameMap gameMap = GameMap_DFHStadium,
    bool skipReplays = false,
    bool instantStart = false,
    flatbuffers::Offset<MutatorSettings> mutatorSettings = 0,
    ExistingMatchBehavior existingMatchBehavior = ExistingMatchBehavior_Restart_If_Different,
    bool enableLockstep = false,
    bool enableRendering = false,
    bool enableStateSetting = false,
    bool autoSaveReplay = false,
    const char *gameMapUpk = nullptr) {
  return rlbot::flat::CreateMatchSettings(
      _fbb,
      playerConfigurations ? _fbb.CreateVector<flatbuffers::Offset<PlayerConfiguration>>(*playerConfigurations) : 0,
      gameMode,
      gameMap,
      skipReplays,
      instantStart,
      mutatorSettings,
      existingMatchBehavior,
      enableLockstep,
      enableRendering,
      enableStateSetting,
      autoSaveReplay,
      gameMapUpk ? _fbb.CreateString(gameMapUpk) : 0);
}

struct QuickChatMessages FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_MESSAGES = 4
  };
  const flatbuffers::Vector<flatbuffers::Offset<QuickChat>> *messages() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<QuickChat>> *>(VT_MESSAGES);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_MESSAGES) &&
           verifier.Verify(messages()) &&
           verifier.VerifyVectorOfTables(messages()) &&
           verifier.EndTable();
  }
};

struct QuickChatMessagesBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_messages(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<QuickChat>>> messages) {
    fbb_.AddOffset(QuickChatMessages::VT_MESSAGES, messages);
  }
  explicit QuickChatMessagesBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  QuickChatMessagesBuilder &operator=(const QuickChatMessagesBuilder &);
  flatbuffers::Offset<QuickChatMessages> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<QuickChatMessages>(end);
    return o;
  }
};

inline flatbuffers::Offset<QuickChatMessages> CreateQuickChatMessages(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<QuickChat>>> messages = 0) {
  QuickChatMessagesBuilder builder_(_fbb);
  builder_.add_messages(messages);
  return builder_.Finish();
}

inline flatbuffers::Offset<QuickChatMessages> CreateQuickChatMessagesDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<flatbuffers::Offset<QuickChat>> *messages = nullptr) {
  return rlbot::flat::CreateQuickChatMessages(
      _fbb,
      messages ? _fbb.CreateVector<flatbuffers::Offset<QuickChat>>(*messages) : 0);
}

/// Sent when connecting to RLBot to indicate what type of messages are desired.
/// This could be sent by a bot, or a bot manager governing several bots, an
/// overlay, or any other utility that connects to the RLBot process.
struct ReadyMessage FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_WANTSBALLPREDICTIONS = 4,
    VT_WANTSQUICKCHAT = 6,
    VT_WANTSGAMEMESSAGES = 8
  };
  bool wantsBallPredictions() const {
    return GetField<uint8_t>(VT_WANTSBALLPREDICTIONS, 0) != 0;
  }
  bool wantsQuickChat() const {
    return GetField<uint8_t>(VT_WANTSQUICKCHAT, 0) != 0;
  }
  bool wantsGameMessages() const {
    return GetField<uint8_t>(VT_WANTSGAMEMESSAGES, 0) != 0;
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_WANTSBALLPREDICTIONS) &&
           VerifyField<uint8_t>(verifier, VT_WANTSQUICKCHAT) &&
           VerifyField<uint8_t>(verifier, VT_WANTSGAMEMESSAGES) &&
           verifier.EndTable();
  }
};

struct ReadyMessageBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_wantsBallPredictions(bool wantsBallPredictions) {
    fbb_.AddElement<uint8_t>(ReadyMessage::VT_WANTSBALLPREDICTIONS, static_cast<uint8_t>(wantsBallPredictions), 0);
  }
  void add_wantsQuickChat(bool wantsQuickChat) {
    fbb_.AddElement<uint8_t>(ReadyMessage::VT_WANTSQUICKCHAT, static_cast<uint8_t>(wantsQuickChat), 0);
  }
  void add_wantsGameMessages(bool wantsGameMessages) {
    fbb_.AddElement<uint8_t>(ReadyMessage::VT_WANTSGAMEMESSAGES, static_cast<uint8_t>(wantsGameMessages), 0);
  }
  explicit ReadyMessageBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ReadyMessageBuilder &operator=(const ReadyMessageBuilder &);
  flatbuffers::Offset<ReadyMessage> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<ReadyMessage>(end);
    return o;
  }
};

inline flatbuffers::Offset<ReadyMessage> CreateReadyMessage(
    flatbuffers::FlatBufferBuilder &_fbb,
    bool wantsBallPredictions = false,
    bool wantsQuickChat = false,
    bool wantsGameMessages = false) {
  ReadyMessageBuilder builder_(_fbb);
  builder_.add_wantsGameMessages(wantsGameMessages);
  builder_.add_wantsQuickChat(wantsQuickChat);
  builder_.add_wantsBallPredictions(wantsBallPredictions);
  return builder_.Finish();
}

/// Notification that a player triggers some in-game event, such as:
///		Win, Loss, TimePlayed;
///		Shot, Assist, Center, Clear, PoolShot;
///		Goal, AerialGoal, BicycleGoal, BulletGoal, BackwardsGoal, LongGoal, OvertimeGoal, TurtleGoal;
///		AerialHit, BicycleHit, BulletHit, /*BackwardsHit,*/ JuggleHit, FirstTouch, BallHit;
///		Save, EpicSave, FreezeSave;
///		HatTrick, Savior, Playmaker, MVP;
///		FastestGoal, SlowestGoal, FurthestGoal, OwnGoal;
///		MostBallTouches, FewestBallTouches, MostBoostPickups, FewestBoostPickups, BoostPickups;
///		CarTouches, Demolition, Demolish;
///		LowFive, HighFive;
struct PlayerStatEvent FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_PLAYERINDEX = 4,
    VT_STATTYPE = 6
  };
  /// index of the player associated with the event
  int32_t playerIndex() const {
    return GetField<int32_t>(VT_PLAYERINDEX, 0);
  }
  /// Event type
  const flatbuffers::String *statType() const {
    return GetPointer<const flatbuffers::String *>(VT_STATTYPE);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_PLAYERINDEX) &&
           VerifyOffset(verifier, VT_STATTYPE) &&
           verifier.Verify(statType()) &&
           verifier.EndTable();
  }
};

struct PlayerStatEventBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_playerIndex(int32_t playerIndex) {
    fbb_.AddElement<int32_t>(PlayerStatEvent::VT_PLAYERINDEX, playerIndex, 0);
  }
  void add_statType(flatbuffers::Offset<flatbuffers::String> statType) {
    fbb_.AddOffset(PlayerStatEvent::VT_STATTYPE, statType);
  }
  explicit PlayerStatEventBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  PlayerStatEventBuilder &operator=(const PlayerStatEventBuilder &);
  flatbuffers::Offset<PlayerStatEvent> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<PlayerStatEvent>(end);
    return o;
  }
};

inline flatbuffers::Offset<PlayerStatEvent> CreatePlayerStatEvent(
    flatbuffers::FlatBufferBuilder &_fbb,
    int32_t playerIndex = 0,
    flatbuffers::Offset<flatbuffers::String> statType = 0) {
  PlayerStatEventBuilder builder_(_fbb);
  builder_.add_statType(statType);
  builder_.add_playerIndex(playerIndex);
  return builder_.Finish();
}

inline flatbuffers::Offset<PlayerStatEvent> CreatePlayerStatEventDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    int32_t playerIndex = 0,
    const char *statType = nullptr) {
  return rlbot::flat::CreatePlayerStatEvent(
      _fbb,
      playerIndex,
      statType ? _fbb.CreateString(statType) : 0);
}

/// Notification when the local player is spectating another player.
struct PlayerSpectate FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_PLAYERINDEX = 4
  };
  /// index of the player that is being spectated. Will be -1 if not spectating anyone.
  int32_t playerIndex() const {
    return GetField<int32_t>(VT_PLAYERINDEX, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_PLAYERINDEX) &&
           verifier.EndTable();
  }
};

struct PlayerSpectateBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_playerIndex(int32_t playerIndex) {
    fbb_.AddElement<int32_t>(PlayerSpectate::VT_PLAYERINDEX, playerIndex, 0);
  }
  explicit PlayerSpectateBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  PlayerSpectateBuilder &operator=(const PlayerSpectateBuilder &);
  flatbuffers::Offset<PlayerSpectate> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<PlayerSpectate>(end);
    return o;
  }
};

inline flatbuffers::Offset<PlayerSpectate> CreatePlayerSpectate(
    flatbuffers::FlatBufferBuilder &_fbb,
    int32_t playerIndex = 0) {
  PlayerSpectateBuilder builder_(_fbb);
  builder_.add_playerIndex(playerIndex);
  return builder_.Finish();
}

/// Rocket League is notifying us that some player has moved their controller. This is an *output*
struct PlayerInputChange FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_PLAYERINDEX = 4,
    VT_CONTROLLERSTATE = 6,
    VT_DODGEFORWARD = 8,
    VT_DODGERIGHT = 10
  };
  int32_t playerIndex() const {
    return GetField<int32_t>(VT_PLAYERINDEX, 0);
  }
  const ControllerState *controllerState() const {
    return GetPointer<const ControllerState *>(VT_CONTROLLERSTATE);
  }
  float dodgeForward() const {
    return GetField<float>(VT_DODGEFORWARD, 0.0f);
  }
  float dodgeRight() const {
    return GetField<float>(VT_DODGERIGHT, 0.0f);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_PLAYERINDEX) &&
           VerifyOffset(verifier, VT_CONTROLLERSTATE) &&
           verifier.VerifyTable(controllerState()) &&
           VerifyField<float>(verifier, VT_DODGEFORWARD) &&
           VerifyField<float>(verifier, VT_DODGERIGHT) &&
           verifier.EndTable();
  }
};

struct PlayerInputChangeBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_playerIndex(int32_t playerIndex) {
    fbb_.AddElement<int32_t>(PlayerInputChange::VT_PLAYERINDEX, playerIndex, 0);
  }
  void add_controllerState(flatbuffers::Offset<ControllerState> controllerState) {
    fbb_.AddOffset(PlayerInputChange::VT_CONTROLLERSTATE, controllerState);
  }
  void add_dodgeForward(float dodgeForward) {
    fbb_.AddElement<float>(PlayerInputChange::VT_DODGEFORWARD, dodgeForward, 0.0f);
  }
  void add_dodgeRight(float dodgeRight) {
    fbb_.AddElement<float>(PlayerInputChange::VT_DODGERIGHT, dodgeRight, 0.0f);
  }
  explicit PlayerInputChangeBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  PlayerInputChangeBuilder &operator=(const PlayerInputChangeBuilder &);
  flatbuffers::Offset<PlayerInputChange> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<PlayerInputChange>(end);
    return o;
  }
};

inline flatbuffers::Offset<PlayerInputChange> CreatePlayerInputChange(
    flatbuffers::FlatBufferBuilder &_fbb,
    int32_t playerIndex = 0,
    flatbuffers::Offset<ControllerState> controllerState = 0,
    float dodgeForward = 0.0f,
    float dodgeRight = 0.0f) {
  PlayerInputChangeBuilder builder_(_fbb);
  builder_.add_dodgeRight(dodgeRight);
  builder_.add_dodgeForward(dodgeForward);
  builder_.add_controllerState(controllerState);
  builder_.add_playerIndex(playerIndex);
  return builder_.Finish();
}

struct GameMessageWrapper FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_MESSAGE_TYPE = 4,
    VT_MESSAGE = 6
  };
  GameMessage Message_type() const {
    return static_cast<GameMessage>(GetField<uint8_t>(VT_MESSAGE_TYPE, 0));
  }
  const void *Message() const {
    return GetPointer<const void *>(VT_MESSAGE);
  }
  template<typename T> const T *Message_as() const;
  const PlayerStatEvent *Message_as_PlayerStatEvent() const {
    return Message_type() == GameMessage_PlayerStatEvent ? static_cast<const PlayerStatEvent *>(Message()) : nullptr;
  }
  const PlayerSpectate *Message_as_PlayerSpectate() const {
    return Message_type() == GameMessage_PlayerSpectate ? static_cast<const PlayerSpectate *>(Message()) : nullptr;
  }
  const PlayerInputChange *Message_as_PlayerInputChange() const {
    return Message_type() == GameMessage_PlayerInputChange ? static_cast<const PlayerInputChange *>(Message()) : nullptr;
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_MESSAGE_TYPE) &&
           VerifyOffset(verifier, VT_MESSAGE) &&
           VerifyGameMessage(verifier, Message(), Message_type()) &&
           verifier.EndTable();
  }
};

template<> inline const PlayerStatEvent *GameMessageWrapper::Message_as<PlayerStatEvent>() const {
  return Message_as_PlayerStatEvent();
}

template<> inline const PlayerSpectate *GameMessageWrapper::Message_as<PlayerSpectate>() const {
  return Message_as_PlayerSpectate();
}

template<> inline const PlayerInputChange *GameMessageWrapper::Message_as<PlayerInputChange>() const {
  return Message_as_PlayerInputChange();
}

struct GameMessageWrapperBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_Message_type(GameMessage Message_type) {
    fbb_.AddElement<uint8_t>(GameMessageWrapper::VT_MESSAGE_TYPE, static_cast<uint8_t>(Message_type), 0);
  }
  void add_Message(flatbuffers::Offset<void> Message) {
    fbb_.AddOffset(GameMessageWrapper::VT_MESSAGE, Message);
  }
  explicit GameMessageWrapperBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  GameMessageWrapperBuilder &operator=(const GameMessageWrapperBuilder &);
  flatbuffers::Offset<GameMessageWrapper> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<GameMessageWrapper>(end);
    return o;
  }
};

inline flatbuffers::Offset<GameMessageWrapper> CreateGameMessageWrapper(
    flatbuffers::FlatBufferBuilder &_fbb,
    GameMessage Message_type = GameMessage_NONE,
    flatbuffers::Offset<void> Message = 0) {
  GameMessageWrapperBuilder builder_(_fbb);
  builder_.add_Message(Message);
  builder_.add_Message_type(Message_type);
  return builder_.Finish();
}

/// We have some very small messages that are only a few bytes but potentially sent at high frequency.
/// Bundle them into a packet to reduce the overhead of sending data over TCP.
struct MessagePacket FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  enum {
    VT_MESSAGES = 4,
    VT_GAMESECONDS = 6,
    VT_FRAMENUM = 8
  };
  const flatbuffers::Vector<flatbuffers::Offset<GameMessageWrapper>> *messages() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<GameMessageWrapper>> *>(VT_MESSAGES);
  }
  float gameSeconds() const {
    return GetField<float>(VT_GAMESECONDS, 0.0f);
  }
  int32_t frameNum() const {
    return GetField<int32_t>(VT_FRAMENUM, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_MESSAGES) &&
           verifier.Verify(messages()) &&
           verifier.VerifyVectorOfTables(messages()) &&
           VerifyField<float>(verifier, VT_GAMESECONDS) &&
           VerifyField<int32_t>(verifier, VT_FRAMENUM) &&
           verifier.EndTable();
  }
};

struct MessagePacketBuilder {
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_messages(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<GameMessageWrapper>>> messages) {
    fbb_.AddOffset(MessagePacket::VT_MESSAGES, messages);
  }
  void add_gameSeconds(float gameSeconds) {
    fbb_.AddElement<float>(MessagePacket::VT_GAMESECONDS, gameSeconds, 0.0f);
  }
  void add_frameNum(int32_t frameNum) {
    fbb_.AddElement<int32_t>(MessagePacket::VT_FRAMENUM, frameNum, 0);
  }
  explicit MessagePacketBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  MessagePacketBuilder &operator=(const MessagePacketBuilder &);
  flatbuffers::Offset<MessagePacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<MessagePacket>(end);
    return o;
  }
};

inline flatbuffers::Offset<MessagePacket> CreateMessagePacket(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<GameMessageWrapper>>> messages = 0,
    float gameSeconds = 0.0f,
    int32_t frameNum = 0) {
  MessagePacketBuilder builder_(_fbb);
  builder_.add_frameNum(frameNum);
  builder_.add_gameSeconds(gameSeconds);
  builder_.add_messages(messages);
  return builder_.Finish();
}

inline flatbuffers::Offset<MessagePacket> CreateMessagePacketDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<flatbuffers::Offset<GameMessageWrapper>> *messages = nullptr,
    float gameSeconds = 0.0f,
    int32_t frameNum = 0) {
  return rlbot::flat::CreateMessagePacket(
      _fbb,
      messages ? _fbb.CreateVector<flatbuffers::Offset<GameMessageWrapper>>(*messages) : 0,
      gameSeconds,
      frameNum);
}

inline bool VerifyCollisionShape(flatbuffers::Verifier &verifier, const void *obj, CollisionShape type) {
  switch (type) {
    case CollisionShape_NONE: {
      return true;
    }
    case CollisionShape_BoxShape: {
      auto ptr = reinterpret_cast<const BoxShape *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case CollisionShape_SphereShape: {
      auto ptr = reinterpret_cast<const SphereShape *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case CollisionShape_CylinderShape: {
      auto ptr = reinterpret_cast<const CylinderShape *>(obj);
      return verifier.VerifyTable(ptr);
    }
    default: return false;
  }
}

inline bool VerifyCollisionShapeVector(flatbuffers::Verifier &verifier, const flatbuffers::Vector<flatbuffers::Offset<void>> *values, const flatbuffers::Vector<uint8_t> *types) {
  if (!values || !types) return !values && !types;
  if (values->size() != types->size()) return false;
  for (flatbuffers::uoffset_t i = 0; i < values->size(); ++i) {
    if (!VerifyCollisionShape(
        verifier,  values->Get(i), types->GetEnum<CollisionShape>(i))) {
      return false;
    }
  }
  return true;
}

inline bool VerifyPlayerClass(flatbuffers::Verifier &verifier, const void *obj, PlayerClass type) {
  switch (type) {
    case PlayerClass_NONE: {
      return true;
    }
    case PlayerClass_RLBotPlayer: {
      auto ptr = reinterpret_cast<const RLBotPlayer *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case PlayerClass_HumanPlayer: {
      auto ptr = reinterpret_cast<const HumanPlayer *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case PlayerClass_PsyonixBotPlayer: {
      auto ptr = reinterpret_cast<const PsyonixBotPlayer *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case PlayerClass_PartyMemberBotPlayer: {
      auto ptr = reinterpret_cast<const PartyMemberBotPlayer *>(obj);
      return verifier.VerifyTable(ptr);
    }
    default: return false;
  }
}

inline bool VerifyPlayerClassVector(flatbuffers::Verifier &verifier, const flatbuffers::Vector<flatbuffers::Offset<void>> *values, const flatbuffers::Vector<uint8_t> *types) {
  if (!values || !types) return !values && !types;
  if (values->size() != types->size()) return false;
  for (flatbuffers::uoffset_t i = 0; i < values->size(); ++i) {
    if (!VerifyPlayerClass(
        verifier,  values->Get(i), types->GetEnum<PlayerClass>(i))) {
      return false;
    }
  }
  return true;
}

inline bool VerifyGameMessage(flatbuffers::Verifier &verifier, const void *obj, GameMessage type) {
  switch (type) {
    case GameMessage_NONE: {
      return true;
    }
    case GameMessage_PlayerStatEvent: {
      auto ptr = reinterpret_cast<const PlayerStatEvent *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case GameMessage_PlayerSpectate: {
      auto ptr = reinterpret_cast<const PlayerSpectate *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case GameMessage_PlayerInputChange: {
      auto ptr = reinterpret_cast<const PlayerInputChange *>(obj);
      return verifier.VerifyTable(ptr);
    }
    default: return false;
  }
}

inline bool VerifyGameMessageVector(flatbuffers::Verifier &verifier, const flatbuffers::Vector<flatbuffers::Offset<void>> *values, const flatbuffers::Vector<uint8_t> *types) {
  if (!values || !types) return !values && !types;
  if (values->size() != types->size()) return false;
  for (flatbuffers::uoffset_t i = 0; i < values->size(); ++i) {
    if (!VerifyGameMessage(
        verifier,  values->Get(i), types->GetEnum<GameMessage>(i))) {
      return false;
    }
  }
  return true;
}

inline const rlbot::flat::QuickChat *GetQuickChat(const void *buf) {
  return flatbuffers::GetRoot<rlbot::flat::QuickChat>(buf);
}

inline const rlbot::flat::QuickChat *GetSizePrefixedQuickChat(const void *buf) {
  return flatbuffers::GetSizePrefixedRoot<rlbot::flat::QuickChat>(buf);
}

inline bool VerifyQuickChatBuffer(
    flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<rlbot::flat::QuickChat>(nullptr);
}

inline bool VerifySizePrefixedQuickChatBuffer(
    flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<rlbot::flat::QuickChat>(nullptr);
}

inline void FinishQuickChatBuffer(
    flatbuffers::FlatBufferBuilder &fbb,
    flatbuffers::Offset<rlbot::flat::QuickChat> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedQuickChatBuffer(
    flatbuffers::FlatBufferBuilder &fbb,
    flatbuffers::Offset<rlbot::flat::QuickChat> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace flat
}  // namespace rlbot

#endif  // FLATBUFFERS_GENERATED_RLBOT_RLBOT_FLAT_H_
