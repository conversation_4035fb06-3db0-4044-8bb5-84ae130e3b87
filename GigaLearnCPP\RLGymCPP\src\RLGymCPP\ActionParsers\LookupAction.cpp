#include "LookupAction.h"

RLGC::LookupAction::LookupAction() {
    bins = {
        {-1, 0, 1}, {-1, 0, 1}, {-1, 0, 1}, {-1, 0, 1}, {-1, 0, 1}
    };
    MakeLookupTable();
}

RLGC::LookupAction::LookupAction(const std::vector<std::vector<float>>& customBins) {
    bins = customBins;
    MakeLookupTable();
}

RLGC::LookupAction::LookupAction(const std::vector<float>& singleBin) {
    bins = {singleBin, singleBin, singleBin, singleBin, singleBin};
    MakeLookupTable();
}

void RLGC::LookupAction::MakeLookupTable() {
    _lookupTable.clear();
    
    // Ground actions
    for (float throttle : bins[0]) {
        for (float steer : bins[1]) {
            for (int boost : {0, 1}) {
                for (int handbrake : {0, 1}) {
                    if (boost == 1 && throttle != 1)
                        continue;
                    
                    float actualThrottle = (throttle != 0) ? throttle : boost;
                    _lookupTable.push_back({
                        actualThrottle, steer, 0, steer, 0, 0, (float)boost, (float)handbrake
                    });
                }
            }
        }
    }
    
    // Aerial actions
    for (float pitch : bins[2]) {
        for (float yaw : bins[3]) {
            for (float roll : bins[4]) {
                for (int jump : {0, 1}) {
                    for (int boost : {0, 1}) {
                        if (jump == 1 && yaw != 0)
                            continue;
                        if (pitch == 0 && roll == 0 && jump == 0)
                            continue;
                        
                        int handbrake = (jump == 1) && (pitch != 0 || yaw != 0 || roll != 0);
                        
                        _lookupTable.push_back({
                            (float)boost, yaw, pitch, yaw, roll, (float)jump, (float)boost, (float)handbrake
                        });
                    }
                }
            }
        }
    }
}
