name: Pip

on:
  workflow_dispatch:
  pull_request:
  push:
    branches:
    - master
    - stable
    - v*
  release:
    types:
    - published

permissions:
  contents: read

env:
  PIP_BREAK_SYSTEM_PACKAGES: 1
  PIP_ONLY_BINARY: numpy

jobs:
  # This builds the sdists and wheels and makes sure the files are exactly as
  # expected. Using Windows and Python 3.6, since that is often the most
  # challenging matrix element.
  test-packaging:
    name: 🐍 3.6 • 📦 tests • windows-latest
    runs-on: windows-latest

    steps:
    - uses: actions/checkout@v4

    - name: Setup 🐍 3.6
      uses: actions/setup-python@v5
      with:
        python-version: 3.6

    - name: Prepare env
      run: |
        python -m pip install -r tests/requirements.txt

    - name: Python Packaging tests
      run: pytest tests/extra_python_package/


  # This runs the packaging tests and also builds and saves the packages as
  # artifacts.
  packaging:
    name: 🐍 3.8 • 📦 & 📦 tests • ubuntu-latest
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Setup 🐍 3.8
      uses: actions/setup-python@v5
      with:
        python-version: 3.8

    - name: Prepare env
      run: |
        python -m pip install -r tests/requirements.txt build twine

    - name: Python Packaging tests
      run: pytest tests/extra_python_package/

    - name: Build SDist and wheels
      run: |
        python -m build
        PYBIND11_GLOBAL_SDIST=1 python -m build

    - name: Check metadata
      run: twine check dist/*

    - name: Save standard package
      uses: actions/upload-artifact@v4
      with:
        name: standard
        path: dist/pybind11-*

    - name: Save global package
      uses: actions/upload-artifact@v4
      with:
        name: global
        path: dist/pybind11_global-*



  # When a GitHub release is made, upload the artifacts to PyPI
  upload:
    name: Upload to PyPI
    runs-on: ubuntu-latest
    if: github.event_name == 'release' && github.event.action == 'published'
    needs: [packaging]

    steps:
    - uses: actions/setup-python@v5
      with:
        python-version: "3.x"

    # Downloads all to directories matching the artifact names
    - uses: actions/download-artifact@v4

    - name: Publish standard package
      uses: pypa/gh-action-pypi-publish@release/v1
      with:
        password: ${{ secrets.pypi_password }}
        packages-dir: standard/

    - name: Publish global package
      uses: pypa/gh-action-pypi-publish@release/v1
      with:
        password: ${{ secrets.pypi_password_global }}
        packages-dir: global/
