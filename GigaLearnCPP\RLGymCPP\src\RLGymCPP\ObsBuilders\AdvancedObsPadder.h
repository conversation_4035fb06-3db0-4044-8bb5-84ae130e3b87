#pragma once
#include "ObsBuilder.h"

namespace RLGC {
    class AdvancedObsPadder : public ObsBuilder {
    public:
        int teamSize;
        bool expanding;
        
        constexpr static float
            POS_STD = 2300.f,
            ANG_STD = 3.14159265359f;

        AdvancedObsPadder(int teamSize = 3, bool expanding = false) 
            : teamSize(teamSize), expanding(expanding) {}

        virtual FList BuildObs(const Player& player, const GameState& state) override;

    private:
        void AddPlayerToObs(FList& obs, const Player& player, const PhysState& ball, bool inverted, PhysState& outPlayerCar);
        void AddDummy(FList& obs);
    };
}