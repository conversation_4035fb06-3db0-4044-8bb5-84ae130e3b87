#pragma once
#include "ActionParser.h"
#include <vector>

namespace RLGC {

    class LookupAction : public ActionParser {
    public:
        std::vector<Action> _lookupTable;
        std::vector<std::vector<float>> bins;

        LookupAction();
        LookupAction(const std::vector<std::vector<float>>& customBins);
        LookupAction(const std::vector<float>& singleBin);

        virtual Action ParseAction(int index, const Player& player, const GameState& state) override {
            return _lookupTable[index];
        }

        virtual int GetActionAmount() override {
            return _lookupTable.size();
        }

    private:
        void MakeLookupTable();
    };
}
