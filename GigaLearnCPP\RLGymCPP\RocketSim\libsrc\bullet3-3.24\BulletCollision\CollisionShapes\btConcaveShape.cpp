/*
Bullet Continuous Collision Detection and Physics Library
Copyright (c) 2003-2009 <PERSON>  http://bulletphysics.org

This software is provided 'as-is', without any express or implied warranty.
In no event will the authors be held liable for any damages arising from the use of this software.
Permission is granted to anyone to use this software for any purpose, 
including commercial applications, and to alter it and redistribute it freely, 
subject to the following restrictions:

1. The origin of this software must not be misrepresented; you must not claim that you wrote the original software. If you use this software in a product, an acknowledgment in the product documentation would be appreciated but is not required.
2. Altered source versions must be plainly marked as such, and must not be misrepresented as being the original software.
3. This notice may not be removed or altered from any source distribution.
*/

#include "btConcaveShape.h"

#include "btBvhTriangleMeshShape.h"
#include "btStaticPlaneShape.h"

btConcaveShape::btConcaveShape() : m_collisionMargin(btScalar(0.))
{
}

btConcaveShape::~btConcaveShape()
{
}

void btConcaveShape::processAllTriangles(btTriangleCallback* callback, const btVector3& aabbMin, const btVector3& aabbMax) const {
	switch (this->getShapeType()) {
	case TRIANGLE_MESH_SHAPE_PROXYTYPE:
		return ((btBvhTriangleMeshShape*)this)->processAllTriangles(callback, aabbMin, aabbMax);
	case STATIC_PLANE_PROXYTYPE:
		return ((btStaticPlaneShape*)this)->processAllTriangles(callback, aabbMin, aabbMax);
	default:
		btAssert(false);
	}
}