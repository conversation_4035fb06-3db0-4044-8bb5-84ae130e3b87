#include "AdvancedObsPadder.h"
#include <RLGymCPP/Gamestates/StateUtil.h>

RLGC::FList RLGC::AdvancedObsPadder::BuildObs(const Player& player, const GameState& state) {
    FList obs = {};

    bool inverted = player.team == Team::ORANGE;
    auto ball = InvertPhys(state.ball, inverted);
    auto& pads = state.GetBoostPads(inverted);

    // Ball info
    obs += ball.pos / POS_STD;
    obs += ball.vel / POS_STD;
    obs += ball.angVel / ANG_STD;

    // Previous action
    for (int i = 0; i < player.prevAction.ELEM_AMOUNT; i++)
        obs += player.prevAction[i];

    // Boost pads
    for (int i = 0; i < CommonValues::BOOST_LOCATIONS_AMOUNT; i++)
        obs += (float)pads[i];

    // Player car
    PhysState playerCar;
    AddPlayerToObs(obs, player, ball, inverted, playerCar);

    FList allies = {}, enemies = {};
    int allyCount = 0, enemyCount = 0;

    for (auto& other : state.players) {
        if (other.carId == player.carId)
            continue;

        if (other.team == player.team) {
            allyCount++;
            if (allyCount > teamSize - 1)
                continue;
            
            PhysState otherCar;
            AddPlayerToObs(allies, other, ball, inverted, otherCar);
            
            // Extra info - relative position and velocity
            allies += (otherCar.pos - playerCar.pos) / POS_STD;
            allies += (otherCar.vel - playerCar.vel) / POS_STD;
        } else {
            enemyCount++;
            if (enemyCount > teamSize)
                continue;
            
            PhysState otherCar;
            AddPlayerToObs(enemies, other, ball, inverted, otherCar);
            
            // Extra info - relative position and velocity
            enemies += (otherCar.pos - playerCar.pos) / POS_STD;
            enemies += (otherCar.vel - playerCar.vel) / POS_STD;
        }
    }

    // Pad with dummy players
    while (allyCount < teamSize - 1) {
        AddDummy(allies);
        allyCount++;
    }

    while (enemyCount < teamSize) {
        AddDummy(enemies);
        enemyCount++;
    }

    obs += allies;
    obs += enemies;

    return obs;
}

void RLGC::AdvancedObsPadder::AddPlayerToObs(FList& obs, const Player& player, const PhysState& ball, bool inverted, PhysState& outPlayerCar) {
    outPlayerCar = InvertPhys(player, inverted);

    Vec relPos = ball.pos - outPlayerCar.pos;
    Vec relVel = ball.vel - outPlayerCar.vel;

    obs += relPos / POS_STD;
    obs += relVel / POS_STD;
    obs += outPlayerCar.pos / POS_STD;
    obs += outPlayerCar.rotMat.forward;
    obs += outPlayerCar.rotMat.up;
    obs += outPlayerCar.vel / POS_STD;
    obs += outPlayerCar.angVel / ANG_STD;

    obs += player.boost / 100.f;
    obs += player.isOnGround;
    obs += player.HasFlipOrJump();
    obs += player.isDemoed;
    obs += player.hasJumped;
}

void RLGC::AdvancedObsPadder::AddDummy(FList& obs) {
    // 8 groups of zeros matching the structure in _add_dummy
    obs += Vec(0, 0, 0); // rel_pos
    obs += Vec(0, 0, 0); // rel_vel  
    obs += Vec(0, 0, 0); // position
    obs += Vec(0, 0, 0); // forward
    obs += Vec(0, 0, 0); // up
    obs += Vec(0, 0, 0); // velocity
    obs += Vec(0, 0, 0); // angular_velocity
    obs += 0.f; obs += 0.f; obs += 0.f; obs += 0.f; obs += 0.f; // boost, ground, flip, demo, jump
    
    // Extra info (relative pos/vel to main player)
    obs += Vec(0, 0, 0); // relative position
    obs += Vec(0, 0, 0); // relative velocity
}
